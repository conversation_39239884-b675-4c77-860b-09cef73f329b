#!/bin/sh
#
# Copyright (c) 2004, 2018, Oracle and/or its affiliates. All rights reserved.

SC_UPDATE_NTP=/usr/cluster/lib/sc/sc_update_ntp
NTP_CONF=/etc/inet/ntp.conf
NTP_INCLUDE=/etc/inet/ntp.conf.include
NTP_SC=/etc/inet/ntp.conf.sc
PTP_PKG=service/network/ptp
SC_DISABLE_NTP=/etc/cluster/notntp

case "$1" in
'start')
	#
	# Test if we are booting as part of a cluster.
	#
	/usr/sbin/clinfo > /dev/null 2>&1
	if [ $? = 0 ] ; then
		#
		# Check if PTP is installed and configured. If so,
		# skip NTP related steps.
		#
		skipntp=0
		clpkg=$(/usr/bin/pkg list -H ${PTP_PKG} 2>/dev/null | /usr/bin/awk '{print $1}')
		if [ -n "${clpkg}" ] ; then
			/usr/bin/svcs svc:/network/ptp:default | /usr/bin/grep 'disabled' > /dev/null 2>&1
			if [ $? -ne 0 ] ; then
				skipntp=1
			fi
		fi
		#
		# If third-party PTP software is used, admin would have
		# created 'notntp' file. If this file exists or if the skipntp
		# flag is set, skip executing the NTP related steps.
		#
		if [ $skipntp -eq 1 ] || [ -f ${SC_DISABLE_NTP} ] ; then
			exit 0
		fi

        	if [ -x ${SC_UPDATE_NTP} ]
        	then
			[ -f ${NTP_CONF} ] || exit 0
			${SC_UPDATE_NTP}

			# create the symbolic link to ntp.conf.sc
			if [ -f ${NTP_SC} ]; then
				/usr/bin/rm -f ${NTP_INCLUDE}
				/usr/bin/ln -s ${NTP_SC} ${NTP_INCLUDE}
			fi
        	fi
	elif [ -h ${NTP_INCLUDE} ] ; then
		# Change it to /dev/null
		/usr/bin/rm -f ${NTP_INCLUDE}
		/usr/bin/ln -s /dev/null ${NTP_INCLUDE}
	fi

	# restart the ntp service
	major_os=`/sbin/uname -r | /usr/bin/awk -F. '{print $1}'`
	minor_os=`/sbin/uname -r | /usr/bin/awk -F. '{print $2}'`
	if [ ${major_os} -gt 5 ] || [ ${major_os} -eq 5 ] && [ ${minor_os} -gt 10 ]; then
		/usr/sbin/svcadm restart svc:/network/ntp:default
	else
		/usr/sbin/svcadm restart svc:/network/ntp4:default
	fi
	;;
*)
	echo "Usage: /etc/init.d/sc_update_ntp start"
	;;
esac
exit 0
