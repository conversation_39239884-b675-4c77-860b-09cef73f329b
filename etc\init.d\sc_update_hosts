#!/bin/sh
#
# Copyright (c) 2004, 2018, Oracle and/or its affiliates. All rights reserved.

SC_UPDATE_HOSTS=/usr/cluster/lib/sc/sc_update_hosts
HOSTSFILE=/etc/inet/hosts

case "$1" in
'start')
	#
	# Test if we are booting as part of a cluster.
	#
	/usr/sbin/clinfo > /dev/null 2>&1
	if [ $? = 0 ] ; then
        	if [ -x ${SC_UPDATE_HOSTS} ]
        	then
			[ -f $HOSTSFILE ] || exit 0
			${SC_UPDATE_HOSTS}
        	fi
	fi
	;;
*)
	echo "Usage: /etc/init.d/sc_update_hosts start"
	;;
esac
exit 0
