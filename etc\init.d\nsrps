#!/bin/sh
#
# Copyright (c) 2003-2013, EMC Corporation.
#
# All rights reserved.
#

LANG=C
export LANG

# Turn on shell debugging if requested

if [ "$1" = "-x" ]
then
    shift
    set -x
fi

private_eval()
{
    cmd="eval $1 ${TEE_CMD} ${OUTPUT}"
    (${cmd})
}

# Override to a different locale if /usr/lib/nsr/LANG exist
[ -r /usr/lib/nsr/LANG ] && . /usr/lib/nsr/LANG

findarch()
{
  if [ -x /bin/uname ]; then
	uname=`/bin/uname`
  elif [ -x /usr/bin/uname]; then
	uname=`/usr/bin/uname`
  else
	echo "ERROR: Cannot find uname"
	exit 1
  fi
}

set_osspecificvar()
{
  if [ "X${uname}" = XSunOS -o "X${uname}" = XLinux ]; then
	NWBINPATH=/usr/sbin
	OUTPUT=/dev/console
	TEE_CMD=">>"
  elif [ "X${uname}" = XHP-UX ]; then
	NWBINPATH=/opt/networker/bin
	OUTPUT=/etc/rc.log
	TEE_CMD="2>&1 | /bin/tee -a"
  elif [ "X${uname}" = XAIX ]; then
	NWBINPATH=/bin
	OUTPUT=/dev/console
	TEE_CMD=">>"
  else
	echo "ERROR: this script supports SunOS, Linux, HP-UX and AIX only" 
	exit 1
  fi
}

#######################################
#####            MAIN              ####
#######################################

findarch
set_osspecificvar
result=0

case $1 in
'start')
private_eval "echo 'starting nsrpsd daemon:'"
if [ -f ${NWBINPATH}/nsrpsd ]; then
	private_eval "${NWBINPATH}/nsrpsd"
	result=$?
	if [ ${result} -eq 0 ]; then
	        (/bin/sleep 15)
	        private_eval "echo ' nsrpsd'"
	else
	    	private_eval "echo ' Error: nsrpsd failed to start'"
	fi
else
  private_eval "echo could not start nsrpsd daemon. ${NWBINPATH}/nsrpsd does not exist."
  result=1
fi
        ;;
'stop')
private_eval "echo stopping nsrpsd daemons:"
if [ -f ${NWBINPATH}/nsrps_shutdown ]; then
	private_eval "${NWBINPATH}/nsrps_shutdown -q"
	private_eval "echo nsrps_shutdown -q"
else
  private_eval "echo could not stop nsrpsd daemon. ${NWBINPATH}/nsrps_shutdown does not exist."
  result=1
fi
        ;;
*)
echo "usage: `basename $0` {start|stop}"
result=1
        ;;
esac

exit ${result}
