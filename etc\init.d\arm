#!/bin/sh
###############################################################################
#
# Description:  Arm Startup/Shutdown 
#
# (c) Copyright 1998, Hewlett-Packard Company, all rights reserved.
#
#
# NOTE:    This script is not configurable!  Any changes made to this
#          script will be overwritten when you upgrade to the next
#          release of ARM.
#
###############################################################################

PATH=/sbin:/bin:/usr/bin:/usr/sbin:/etc:/usr/etc:/opt/perf/bin:$PATH
export PATH


if [ -f /etc/default/arm ] ; then
  . /etc/default/arm
else
  echo "ERROR: /etc/default/arm file MISSING"
  exit 1
fi

if [ "$ARM_START" -ne 1 ]
then
  exit 0
fi

case "$1" in
'start')

        if [ -x /opt/perf/bin/ttd -a "$ARM_START" -eq 1 ]
        then
           /opt/perf/bin/ttd
        fi
        ;;

'stop')

        if [ -x /opt/perf/bin/ttd ]
        then
           /opt/perf/bin/ttd -k
        fi
        ;;
*)
	echo "Usage: $0 { start | stop }"
	exit 1
	;;
esac

exit 0











