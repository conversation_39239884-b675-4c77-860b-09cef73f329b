#!/bin/sh

# IMPORTANT NOTE:
# Upgrading NetWorker will overwrite this startup script.
# End-users should place NetWorker environment variables in nsrrc, which will
# be sourced before starting NetWorker daemons. nsrrc must be a Bourne shell
# script, and environment variables in nsrrc must be exported to take effect.
NSRRC=/nsr/nsrrc
NSR_ENVEXEC=/opt/nsr/admin/nsr_envexec

# networkerrc defines environment variables, such as LD_LIBRARY_PATH, required
# to run NetWorker daemons.
NETWORKERRC=/opt/nsr/admin/networkerrc

NSREXECD=/usr/sbin/nsrexecd
NSRPSD=/usr/sbin/nsrpsd
LGTOLICPKG=LGTOlicm
NSR_SHUTDOWN=/usr/sbin/nsr_shutdown
CLUSTER=/usr/sbin/NetWorker.clustersvr
PKG_INSTALL_ROOT=
SRVRPKGNAME=LGTOserv

case $1 in
'start')
(echo 'starting NetWorker daemons:') > /dev/console
if [ -x "${NSREXECD}" ]; then
	if [ -f "${CLUSTER}" ]; then
		if [ -d "${PKG_INSTALL_ROOT}/nsr.NetWorker.local" -o -h "${PKG_INSTALL_ROOT}/nsr.NetWorker.local" ]; then
			if [ -h "${PKG_INSTALL_ROOT}/nsr" ]; then
				rm -f ${PKG_INSTALL_ROOT}/nsr
				ln -s ${PKG_INSTALL_ROOT}/nsr.NetWorker.local ${PKG_INSTALL_ROOT}/nsr
			fi
		fi
	fi
	"$NSR_ENVEXEC" -u "$NSRRC" -s "$NETWORKERRC" "$NSREXECD" 2>&1 | \
		/usr/bin/tee /dev/console
	(echo ' nsrexecd') > /dev/console
	if [ -x "${NSRPSD}" ]; then
		"${NSRPSD}" -i
	fi
fi
;;
'stop')
(echo 'stopping NetWorker daemons:') > /dev/console
if [ -x "${NSR_SHUTDOWN}" ]; then
	(${NSR_SHUTDOWN} -q) 2>&1 | /usr/bin/tee /dev/console
	(echo ' nsr_shutdown -q') > /dev/console
fi
;;
*)
echo "usage: `basename $0` {start|stop}"
;;
esac
