top 
crontab -e
svcadm restart cron
ca /var/tmp/recasting_cts
cd /var/tmp/recasting_cts
ls -lrt
cat Remove_R1_SI.sh 
cat Add_R1.sh 
history 
top
ls -lrt
cat Remove_R1_SI_Path.sh
./Remove_R1_SI.sh
cat Remove_R1_SI.sh
ldm ls
ldm ls -o disk PCTSGW1
ldm ls -o disk PCTSTCSVR1
top
ls -lrt
cat Remove_R1_SI_Path.sh
./Remove_R1_SI_Path.sh
cfgadm -al
dlmsetconf
dlmcfgmgr -a
ls -lrt
cat Add_R1.sh 
./Add_R1.sh 
ldm ls -o disk PCTSGW1
dlnkmgr view -lu -pstv
dlnkmgr view -lu -pstv | grep -i offline
cd /var/sysadm/script/
ls -lrt | grep -i cts
./cgSISyncSuspendCron_GW_TC_CTSApp.sh
pairdisplay -ISI0 -g 
 pairdisplay -ISI0 -g SICTSAPPCG1 -fcxe
top
crontab -e
crontab -l
crontab -e
crontab -l
svcadm restart cron
sysdef
sysadef;date;hostname > /var/tmp/pctsappcdom1.txt 
sysdef;date;hostname > /var/tmp/pctsappcdom1.txt 
cd /var/tmp
ls -ltr
cat  pctsappcdom1.txt
hostname > /var/tmp/pctsappcdom1.txt; date >> /var/tmp/pctsappcdom1.txt ; sysdef >> /var/tmp/pctsappcdom1.txt 
ls -ltr
cat pctsappcdom1.txt
less pctsappcdom1.txt
chown sysa_zsc:sysadmin /var/tmp/pctsappcdom1.txt
ls -ltr
sftp sysa_sah@*************
vi /root/.ssh/known_hosts
sftp sysa_sah@*************
ls -ltr
 cp -p horcm0.conf  sav/hocrm0.conf.20240503
 cp -p horcm1.conf  sav/hocrm1.conf.20240503
ls -trl sav/
pwd
ls -tlr
vi horcm0.conf 
vi horcm0.conf 
vi horcm1.conf 
svcs -va|grep hor
svcadm restart horcm
svcs -va|grep hor
svcs -va|grep hor
svcs -va|grep hor
svcs -va|grep hor
svcs -va|grep hor
svcs -va|grep hor
svcs -va|grep hor
svcs -va|grep hor
svcs -va|grep hor
pairdisplay -IM0 -g SICTSDBCG2 -fxce
pairdisplay -IM0 -g SICTSDBCG3 -fxce
top
raidcom get resource -I200
cd /var/sysadm/script/DRP/
ls -lrt
./Site_Swap_OC2-OC1_GAD.sh 
pairdisplay -g GADCTSDBCG1 -IH200 -fxce
pairdisplay -g GADARCTSDBCG1 -IH200 -fxce
pairdisplay -g GADCTSAPPCG1 -IH200 -fxce
pairdisplay -g GADCTSNASCG1 -IH200 -fxce
ldm ls
cd /var/sysadm/script/DRP
ls
ls -al
./init6ToClusterMode.sh
top
telnet 0 5000
ldm start PCTSTCSVR1
ldm ls
telnet 0 5000
top
ldm ls
telnet 0 5000
ldm ls
telnet 0 5001
top
ldm ls
telnet 0 5001
top
top
./recHDLMPaths.sh
bash
clrg status
crontab -l
op
passwd sysa_sp
crontab -l
less /var/log/cron
less /var/cron/log
crontab -l
svcs -a | grep cron
svcadm restart cron
svcs -a | grep cron
top
passwd -s -a | grep sysa
passwd sysa_orz
vi /etc/security/passhistory 
passwd sysa_orz
passwd -u sysa_orz
passwd -s sysa_orz
passwd -s sysa_ts
passwd sysa_ts
svcs -a | grep cron
top
cd /var/sysadm/
ls -l
cd cronjob/
ls -lrt
cat root_cron_orig.txt 
crontab -l
crontab -l > /export/home/<USER>
crontab -l
crontab -e
crontab -l
svcs -a | grep cron
svcadm restart cron
svcs -a | grep cron
cd /export/home/
ls -lrt
mv root_cron_20240704 sysa_orz/
ls -lrt
cd sysa_orz/
ls -lrt
chown sysa_orz:sysadmin *
ls -l
ls -lart
crontab -l
crontab -l
date; crontab -l
mv output output_20240804
mkdir output
bash main.sh -x scan
cd output
ls -l
cp cis_PCTSGW1.html /var/tmp
cd /var/tmp/
chmod 755 cis_PCTSAPPCDOM1.html
ls -lart
cp /root/cis2/output/cis_PCTSAPPCDOM1.html /var/tmp
chmod 755 cis_PCTSAPPCDOM1.html 
top
passwd -u sysa_am
passwd -u sysa_ks
passwd ssya_am
passwd sysa_am
passwd sysa_ks
top
passwd -s svc_tenable
passwd -u svc_tenable
passwd -s admin_tenable
passwd svc_tenable
passwd admin_tenable
 grep PCIE /var/adm/messages*
history 
passwd -s sysa_dl
cd /etc/
cat horcm0.conf 
pwd
df -h
top
pwd
cd /var/sysadm/script/DRP/
ls -ltr
cp -p Site_Swap_OC2-OC1_GAD.sh sav/Site_Swap_OC2-OC1_GAD.sh.20240907
pwd
ls -ltr
cd ~sysa_dl/
ls -ltr
cd HNASTechRefresh/
ls -ltr
cd script/
ls -ltr
cd DRP/
ls -ltr
diff Site_Swap_OC2-OC1_GAD.sh_G350 /var/sysadm/script/DRP/Site_Swap_OC2-OC1_GAD.sh 
diff Site_Swap_OC2-OC1_GAD.sh_G350 /var/sysadm/script/DRP/sav/Site_Swap_OC2-OC1_GAD.sh.20240907 
cp  Site_Swap_OC2-OC1_GAD.sh_G350 /var/sysadm/script/DRP/Site_Swap_OC2-OC1_GAD.sh 
pwd
df -h
cd -
cd /var/sysadm/script/
pwd
ls -ltr
cd DRP/
ls -tlr
diff sav/Site_Swap_OC2-OC1_GAD.sh.20240907 Site_Swap_OC2-OC1_GAD.sh 
top
ls -ltr
clear
diff Site_Swap_OC2-OC1_GAD.sh sav/Site_Swap_OC2-OC1_GAD.sh.20240907
top
ls -tlr
diff Site_Swap_OC2-OC1_GAD.sh sav/Site_Swap_OC2-OC1_GAD.sh.20240907
top
pwd
ls -tlr
cd /etc/
ls -ltr
cp -p horcm200.conf sav/horcm200.conf.20240907
cp -p horcm201.conf sav/horcm201.conf.20240907
pwd
ls -tlr
vi horcm201.conf 
diff horcm201.conf sav/horcm201.conf.20240907
clear
pwd
ls -tlr
top
ls -tlr
ls -ltr horcm*
ls -trl sav/
ls -tlr
vi horcm200.conf 
diff horcm201.conf sav/horcm201.conf.20240907
diff horcm200.conf sav/horcm200.conf.20240907
pwd
top
pwd
cd /var/sysadm/script/
pwd
ls -ltr
cd DRP/
ls -tlr
vi Site_Swap_OC2-OC1_GAD.sh 
bash Site_Swap_OC2-OC1_GAD.sh 
to
top
pwd
ls -ltr
scp -p Site_Swap_OC2-OC1_GAD.sh sysa_dl@pctsappcdom2:/tmp
top
pwd
ls -trl /etc/horcm*
top
more Site_Swap_OC2-OC1_GAD.sh 
   top
pwd
df -h
ls -ltr
diff Site_Swap_OC2-OC1_GAD.sh sav/Site_Swap_OC2-OC1_GAD.sh.20240909
diff Site_Swap_OC2-OC1_GAD.sh sav/Site_Swap_OC2-OC1_GAD.sh.20240907
more Site_Swap_OC2-OC1_GAD.sh 
 cd sav/
ls -ltr
vi Site_Swap_OC2-OC1_GAD.sh.20240907 
cd ..
diff Site_Swap_OC2-OC1_GAD.sh sav/Site_Swap_OC2-OC1_GAD.sh.20240907
top
ls -lrt
cat horcm0.conf
history
cat horcm0.conf
pairdisplay -IM0 -g SIARCTSDBCG1 -fxce
pairdisplay -IM0 -g SIARCTSDBCG1 -fxce
pairdisplay -IM0 -g SICTSAPPCG1 -fxce
top
passwd sysa_ts
passwd -as | grep AL
cd /root
ls -ltr
cd cis2
ls -ltr
cp -R output /var/tmp/PCTSAPPCDOM1_output
cd /var/tmp
ls -ltr
sftp sysa_sah@pdwsyssftp1
sftp sysa_sah@*************
passwd sysa_ks
passwd sysa_sah
cd /export/home/<USER>
ls -ltr
less PCTS Recasting Solaris Commands_v1.txt
less "PCTS Recasting Solaris Commands_v1.txt"
ls -ltr
mv "PCTS Recasting SAN Commands.txt" "PCTS Recasting Solaris Commands.txt" "PCTS Recasting SAN Commands_v1.txt" "PCTS Recasting Solaris Commands_v1.txt" /export/home/<USER>
ls -ltr
cd ../sysa_zsc
ls -ltr
cd ..
cd sysa_dl
ls -ltr
mv HowTos_WTB.zip HowTos_OTC2.zip /export/home/<USER>
cd ..
userdel -r sysa_dl
id -a sysa_dl;date
useradd -u 1016 -g 14 -G 13 -m -d /export/home/<USER>"System Administrator" -s /bin/bash sysa_dsr
useradd -u 1019 -g 14 -G 13 -m -d /export/home/<USER>"System Administrator" -s /bin/bash sysa_rfb
passwd -x 90 -n 1 sysa_dsr;passwd -x 90 -n 1 sysa_rfb
id -a sysa_dsr
passwd sysa_dsr
passwd sysa_rfb
ls -ltr
chmod 700 sysa_dsr sysa_rfb
ls -ltr
usermod -R sys_admin sysa_dsr;usermod -R sys_admin sysa_rfb
cat /etc/user_attr
id -a sysa_dsr;id -a sysa_rfb;date
passwd -u sysa_orz
passwd -u sysa_am
passwd -u sysa_kl
passwd -u sysa_ks
passwd sysa_orz
passwd sysa_am
passwd sysa_ks
passwd -s -a | grep sysa
passwd sysa_zsc
exit
crontab -l
crontab -l > /var/tmp/`hostname`_root_cron_20250301.txt
clear
crontab -l
crontab -l
crontab -e
crontab -e
ls -lrt /var/tmp/ | grep cron
cat PCTSAPPCDOM1_root_cron_20250301.txt
cat /var/tmp/PCTSAPPCDOM1_root_cron_20250301.txt
crontab -l > /var/tmp/`hostname`_root_cron_20250301_after.txt
cd /var/tmp/
ls -lrt | grep cron
diff PCTSAPPCDOM1_root_cron_20250301.txt PCTSAPPCDOM1_root_cron_20250301_after.txt 
crontab -l
df -h
cd /var/tmp/
ls -lrt | grep cron
diff PCTSAPPCDOM1_root_cron_20250301.txt PCTSAPPCDOM1_root_cron_20250301_after.txt 
scp PCTSAPPCDOM1_*cron* sysa_orz@*************:/sftp/orz/cts/2025
clear
ldm list-devices -a mem
top
clear
ldm list-devices -a mem
ldm ls
ldm ls
top
date; list-devices -a mem
clear
date; ldm list-devices -a mem
clrg status
ldm ls
ldm start PCTSGW1
ldm start PCTSTCSVR1
telnet 0 5000
telnet 0 5001
top
beadm list
ntpq -p
pkg list -af entire
pkg list -af ha-cluster incorporation
pkg list publisher
pkg list-publisher
pkg publisher
pkg list -af ha-cluster
pkg list -af | grep cluster
ls -lrt /usr/ccs/bin/yacc
ls -lrt /usr/sbin/snoop
ls -lrt /usr/bin/su
ls -ld /var/share/audit
chmod 554 /usr/ccs/bin/yacc
chmod 700 /usr/sbin/snoop
chmod 4750 /usr/bin/su
chmod 750 /var/share/audit
ls -lrt /usr/ccs/bin/yacc
ls -lrt /usr/sbin/snoop
ls -lrt /usr/bin/su
ls -ld /var/share/audit
ls -ld /var/tmp
chmod 777 /var/tmp
chmod 775 /var/tmp
ls -ld /var/tmp
ls -lrt /var/sysadm/backup/| tail
cd /root/S11OSPH_V10
./Step3-HardenSol11Svr_v1_CDOMs.sh 
ls -lrt /var/sysadm/backup/| tail
ls -lrt /usr/ccs/bin/yacc
ls -lrt /usr/sbin/snoop
ls -lrt /usr/bin/su
ls -ld /var/share/audit
chown sysa_orz /var/sysadm/backup/*20250419*
cd /etc/
cd net-snmp/snmp/
cp -p snmpd.conf snmpd.conf_20250421
vi snmpd.conf
vi snmpd.conf
cat snmpd.conf | grep -i public
svcadm restart svc:/application/management/net-snmp
svcadm status svc:/application/management/net-snmp
svcs status svc:/application/management/net-snmp
less snmpd.conf
sftp sysa_sah@*************
ls -ltr
cd guds
ls -ltr
unzip guds_3_15.zip 
ls -ltr
cat command.txt 
ls -ltr
cd guds
chmod +x guds
./guds -q -r -T -X 3 -c 30 -i 1 -n 5 -w 0 -s 71307 -d "High Memory Utilization"
cd ..
ls -ltr
tar -czvf guds_pctsappcdom1.tar guds_data
explorer
ls -ltr
sftp sysa_sah@*************
cd /var/explorer/output/
ls -ltr
sftp sysa_sah@*************
ls -ltr
rm explorer.8676e9d2.PCTSAPPCDOM1-2025.***********.tar.gz
cd /var/tmp
ls -ltr
rm guds_pctsappcdom1.tar
rm -rf guds_data
rm -rf guds
history | grep -i explorer
explorer
cd /etc/explorer/default/
ls -lrt
cat explorer 
cd /var/explorer/output
clear
ls -lrt
chmod 755 explorer.8676e9d2.PCTSAPPCDOM1-2025.***********.tar.gz
pwd
du -sh *
df -h
ls -l
ls -l /tmp
ls -l /var/tmp/
cd /tmp/
ls -l
cd /var/explorer/output
ls -lrt
cp -r explorer.8676e9d2.PCTSAPPCDOM1-2025.*********** /tmp
cp -r explorer.8676e9d2.PCTSAPPCDOM1-2025.***********.tar.gz /tmp
cd /tmp/
clear
ls -l
ls -lrt
chmod -R 755 explorer.8676e9d2.PCTSAPPCDOM1-2025.*********** explorer.8676e9d2.PCTSAPPCDOM1-2025.***********.tar.gz
cd /tmp/
ls -lrt
chmod 755 explorer.8676e9d2.PCTSAPPCDOM1-2025.***********.tar.gz
dmesg 
dmesg | grep -i down
clear
dmesg | grep -i down
dladm show-phys
explorer
ps -ef | grep -i explorer
kill -9 18726
ps -ef | grep -i explorer
clear
top
clear
top
ifconfig -a
dladm show-phys
ipadm create-ip net7
dladm show-phys
ipadm create-ip net11
dladm show-phys
ifconfig -a
clear
explorer
top
top
passwd sysa_zsc
passwd svc_tenable
passwd admin_tenable
passwd -as | grep tenable;date
