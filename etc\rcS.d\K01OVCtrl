#!/usr/xpg4/bin/sh
# 
# @(#)BegWS :1.0
# @(#)CR    :Copyright 1988 - 2024 Open Text
# @(#)Name  :Control
# @(#)Ver   :12.27.005
# @(#)FileN :OVCtrl
# @(#)FileV :12.27.005
# @(#)Rtype :Released
# @(#)CBID  :2024-09-16_0134
# @(#)OS    :Solaris
# @(#)OSV   :10
# @(#)Arch  :SPARC
# @(#)Bits  :32
# @(#)Desc  :Control Script
# @(#)Patch :
# @(#)Build :0000
# @(#)EndWS :
# 
#!/usr/xpg4/bin/sh

SCRIPT_NAME="$0"
NAMESPACE="ctrl"
ATTRIBUTE="START_ON_BOOT"
ATTRIBUTE_RUN="RUN_PROFILE"
SERVICE_NAME="OVCtrl"
ATTRIBUTE_INT="START_CHK_INTERVAL"

OV_BIN="/opt/OV/bin"

SU_VAR="/usr/bin/su"

           PATH=/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin;export PATH;

usage()
{
    echo "USAGE: OVCtrl start|stop|restart|START|STOP|enable|disable|status" 1>&2
    exit 1
}

is_ovc_running()
{
   sleep 1
    OS_MINOR_VERSION=`uname -r | awk '{FS="."} {print $2}'`
    if [ $OS_MINOR_VERSION -gt 9 ]; then
        if [ `zonename` = "global" ]
        then
		    PSCOUNT=`ps -ef -Z | egrep -e ' ovc -' -e '/ovc -' | grep -v grep | grep global | wc -l`
		    PSCOUNT1=`ps -ef -Z | egrep -e ' ovc -' | grep -v grep | grep global | wc -l`
		    PSCOUNT2=`ps -ef -Z | egrep -e ' ovc ' | grep -v grep | grep global | wc -l`
        else
		    PSCOUNT=`ps -ef | egrep  -e ' ovc -' -e '/ovc -' |  grep -v grep | wc -l`
		    PSCOUNT1=`ps -ef | egrep  -e ' ovc -' |  grep -v grep | wc -l`
		    PSCOUNT2=`ps -ef | egrep  -e ' ovc ' |  grep -v grep | wc -l`
        fi
    else
		    PSCOUNT=`ps -ef | egrep  -e ' ovc -' -e '/ovc -' |  grep -v grep | wc -l`
		    PSCOUNT1=`ps -ef | egrep  -e ' ovc -' |  grep -v grep | wc -l`
		    PSCOUNT2=`ps -ef | egrep  -e ' ovc ' |  grep -v grep | wc -l`
    fi 
    if [ $PSCOUNT -eq 0  -a  $PSCOUNT1 -eq 0 -a $PSCOUNT2 -eq 0 ]   
    then
        return 0
    else
        return 1
    fi	
}

start_service()
{
    OASTARTDELAY=`$OV_BIN/ovconfget ctrl START_WITH_DELAY` 2>/dev/null

    if [[ $OASTARTDELAY > 0 ]]; then
        #ovconfchg -ns ctrl -set START_WITH_DELAY 30
        #Start OA after 30 secs   
        echo "Start OA service after $OASTARTDELAY sec"
        sleep $OASTARTDELAY
    fi

    if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE`" = "true" ]
    then
       if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE_RUN`" = "true" ]
       then
           /opt/OV/bin/ovc -start -boot
           RETVAL=$?
       else
           PATH=/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin; export PATH;$OV_BIN/ovc -start -boot
           RETVAL=$?
       fi
       sleep 3
        return $RETVAL

    else
        echo "The service $SERVICE_NAME has been disabled. Please run the command '$SCRIPT_NAME enable' to enable it or use '
$SCRIPT_NAME [START|STOP]' to start or stop the service manually."
    fi
}

stop_service()
{
    if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE`" = "true" ]
    then
        if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE_RUN`" = "true" ]
        then
            /opt/OV/bin/ovc -kill
           RETVAL=$?
        else
           PATH=/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin; export PATH;$OV_BIN/ovc -kill
           RETVAL=$?
        fi
        
        return $RETVAL
     
    else
           echo "The service $SERVICE_NAME has been disabled. Please run the command '$SCRIPT_NAME enable' to enable it or use '
$SCRIPT_NAME [START|STOP]' to start or stop the service manually."
    fi
}

restart_service()
{
    if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE`" = "true" ]
    then
        if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE_RUN`" = "true" ]
        then
           /usr/bin/su root -c "/opt/OV/bin/ovc -restart"
           return $?
        else
           PATH=/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin; export PATH;$OV_BIN/ovc -restart
           return $?
        fi
    else
           echo "The service $SERVICE_NAME has been disabled. Please run the command '$SCRIPT_NAME enable' to enable it or use '
$SCRIPT_NAME [START|STOP]' to start or stop the service manually."
    fi
}

enable_service()
{
    if ! $OV_BIN/ovconfchg -ns $NAMESPACE -set $ATTRIBUTE true
    then
        echo "Unable to set the required settings to enable the service $SERVICE_NAME." 1>&2
        return 1
    else
        echo  "The service $SERVICE_NAME is now enabled and will be started during the next bootup."
        return 0
    fi
}

disable_service()
{
    if ! $OV_BIN/ovconfchg -ns $NAMESPACE -set $ATTRIBUTE false
    then
        echo "Unable to set the required settings to disable the service $SERVICE_NAME." 1>&2
        return 1
    else
        echo  "The service $SERVICE_NAME is now disabled and will not be started during the next bootup."
        return 0
    fi
}
if [ $# -ne 1 ]; then
    usage
fi

targetval=$1

count=1
retval_ovc_chk=1

if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE_INT`" ]
    then
        start_chk_interval=`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE_INT`
    else
        start_chk_interval=180
fi

while [ $retval_ovc_chk -ne 0 ]
do
        if [ $count -gt $start_chk_interval ]
        then
                echo "ovc instance still running ..proceeding further"
        break
        fi
        count=`expr $count + 1`
        is_ovc_running
        retval_ovc_chk=$?
done
 
case $targetval in

    start_msg)
        echo "Starting Control Daemon"
        ;;

    stop_msg)
        echo "Stopping Control Daemon"
        ;;

    'start')
           if [ `/usr/xpg4/bin/find /etc/  -type f -name 'S[0-9]*vcs' 2>/dev/null | /usr/bin/wc -l` -ne 0 ]
           then
                /usr/bin/sleep 90
           fi
           start_service
        ;;
    'stop')
        stop_service
        ;;

    'restart')
        restart_service
        ;;

    START)

       if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE`" = "true" ]
       then
           if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE_RUN`" = "true" ]
           then
                /opt/OV/bin/ovc -start -boot
               exit $?
           else
               PATH=/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin; export PATH;$OV_BIN/ovc -start -boot
              exit $?
           fi
       fi
       ;;

    STOP)
       if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE`" = "true" ]
       then
           if [ "`$OV_BIN/ovconfget $NAMESPACE $ATTRIBUTE_RUN`" = "true" ]
           then
                /opt/OV/bin/ovc -kill
              exit $?
           else
              PATH=/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin;export PATH; $OV_BIN/ovc -kill
              exit $?
           fi
       fi
       ;;

    enable)
        enable_service
        exit $?
        ;;
    disable)
        disable_service
        exit $?
        ;;
    status)
        $OV_BIN/ovc -status
        exit $?
        ;;
    *)
        usage
        ;;
esac

