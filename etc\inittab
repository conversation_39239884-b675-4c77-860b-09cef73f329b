#
# Copyright (c) 1988, 2016, Oracle and/or its affiliates. All rights reserved.
#
# The /etc/inittab file controls the configuration of init(8); for more
# information refer to init(8) and inittab(5).  It is no longer
# necessary to edit inittab(5) directly; administrators should use the
# Solaris Service Management Facility (SMF) to define services instead.
# Refer to smf(7) and the System Administration Guide for more
# information on SMF.
#
# For modifying parameters passed to ttymon, use svccfg(8) to modify
# the SMF repository. For example:
#
#	# svccfg
#	svc:> select system/console-login:default
#	svc:/system/console-login> setprop ttymon/terminal_type = "xterm"
#	svc:/system/console-login> refresh
#	svc:/system/console-login> exit
#
dev::sysinit:/usr/sbin/devfsadm -P
ap::sysinit:/usr/sbin/autopush -f /etc/iu.ap
smf::sysinit:/lib/svc/bin/svc.startd >/dev/msglog 2>&1 </dev/console
p3:s1234:powerfail:/usr/sbin/shutdown -y -i5 -g0 >/dev/msglog 2>&1
