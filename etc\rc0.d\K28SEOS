#!/bin/sh
#
# -------------------------------------------------------------------------
# AccessControl v12.81-0
# VeRsIoN: 12.81-0 (2391) Compiled On:Script
# -------------------------------------------------------------------------

SEOSDIR="/opt/CA/AccessControl" 
##################################
##AUTO_INSERT_CODE  -- start here.
if [ $__SEOS__ ] ; then
   SEOSINIPATH=$__SEOS__
else
   if [ -d "/opt/CA/AccessControl" -a -f "/opt/CA/AccessControl/seos.ini" ] ; then
      SEOSINIPATH="/opt/CA/AccessControl"
   else
      if [ -d "/opt/CA/eTrustAccessControl" -a -f "/opt/CA/eTrustAccessControl/seos.ini" ] ; then
          SEOSINIPATH="/opt/CA/eTrustAccessControl"
      else
        if [ -d "/usr/seos/" -a -f "/usr/seos/seos.ini" ] ; then
          SEOSINIPATH="/usr/seos"
        else
          if [ -f "/etc/seos.ini" ] ; then
            if [ -h "/etc/seos.ini" ]; then
               t_file=`ls -l /etc/seos.ini | awk -F'>' '{print $2}' `
               if [ ! -f ${t_file} ]; then
                 echo "Could not find seos.ini. Set __SEOS__ and try again." #L10N
                 exit 1
               else
                 SEOSINIPATH="/etc"
               fi
            else
               SEOSINIPATH="/etc"
            fi
          else
            echo "Could not find seos.ini. Set __SEOS__ and try again." #L10N
            exit 1
          fi
        fi
      fi
   fi
fi
if [ -n "$SEOSINIPATH" ]; then
  if [ -f $SEOSINIPATH/seos.ini ] ; then
    SEOSDIR=`grep "^SEOSPATH" $SEOSINIPATH/seos.ini | grep "=" | awk -F= '{print $2}' | awk '{print $1}'`
  fi
fi

##AUTO_INSERT_CODE  -- ends here.
##################################

# B521906 - Check if an OS patch used our syscall number. If it has calculate
#	    a new one.
Check_for_dup_sysnum()
{
# Calculate SEOS_syscall number and see if it is used by somebody else.
# If it is recalculate a new syscall number
    sys_num=`grep "SEOS_syscall" /etc/name_to_sysnum | awk '{print $2}'`
    occur=`grep -c "$sys_num" /etc/name_to_sysnum`
    if [ $occur -eq 1 ]; then
        return 0
    fi
    grep -v "SEOS_syscall" /etc/name_to_sysnum > /etc/name_to_sysnum_new
    mv /etc/name_to_sysnum_new /etc/name_to_sysnum
    grep SEOS_syscall /etc/name_to_sysnum > /dev/null 2>&1
    if [ "$?" = 0 ]
    then
        echo "K28SEOS: Duplicate syscall number for SEOS_syscall. Error removing SEOS_syscall from /etc/name_to_sysnum."
        /usr/bin/logger "K28SEOS: Duplicate syscall number for SEOS_syscall. Error removing SEOS_syscall from /etc/name_to_sysnum."
        exit 1
    fi
    Check_for_sysnum
    if [ $? -eq 1 ]
    then
        echo "K28SEOS: Error calculating a syscall number for SEOS_syscall."
        /usr/bin/logger "K28SEOS: Error calculating a syscall number for SEOS_syscall."
        exit 1
    fi
    Solaris_sysnum
    if [ $? -eq 1 ]
    then
        echo "K28SEOS: Error applying the SEOS_syscall entry to /etc/name_to_sysnum."
        /usr/bin/logger "K28SEOS: Error applying the SEOS_syscall entry to /etc/name_to_sysnum."
        exit 1
    fi
    return 0
}

# Look for a vacant syscall syscall slot by searching up then down in the syscall table
# Returns:
#         syscall number if vacant slot found
#         0 if slot not found
find_syscall_slot()
{
    # build a list of free slots from /etc/name_to_sysnum
    free_slots=`awk '{print $2}' /etc/name_to_sysnum | sort -n | uniq | \
                awk 'BEGIN {x=0} {while($1>x){print x;x+=1}x+=1} END {print x}'`

    start=$1         # starting point for the search
    max_syscall=$2   # end of the table
    syscall_number=0 # initialise syscall_number as not found
    rev_slots=""     # Build a list of slots in reverse order for searching through

    # First try searching up from the starting point
    for i in $free_slots
    do
       if [ $i -ge $start -a $i -lt $max_syscall ]; then
          # found a vacant slot
          syscall_number=$i
          break
       else
          # build the reverse list of slots
          rev_slots="$i $rev_slots"
       fi
    done
    if [ $syscall_number -eq 0 ]; then
       # empty slot not found, so try searching down
       for i in $rev_slots
       do
          if [ $i -lt $start -a $i -lt $max_syscall ]; then
             # found a vacant slot
             syscall_number=$i
             break
          fi
       done
    fi
    return $syscall_number
}

# Search for available sysnum so SEOS _syscall can use it in /etc/name_to_sysnum
Check_for_sysnum()
{
  if [ -f /etc/name_to_sysnum ]
  then
    if [ -f /usr/include/sys/systm.h ] ; then
       max_syscall=`/bin/grep NSYSCALL /usr/include/sys/systm.h | awk '{print $3}'`
    fi
    max_syscall=${max_syscall:-250}
    syscall_start=101
    find_syscall_slot $syscall_start $max_syscall
    if [ $? -eq 0 ]
    then
      echo ""
      echo "K28SEOS: ERROR, AccessControl must use empty entry in /etc/name_to_sysnum"
      echo "                  All the entres are occupied."
      echo "                  You must free an entry in /etc/name_to_sysnum"
      echo "                  Aborting K28SEOS"
      /usr/bin/logger "K28SEOS: AccessControl must use an empty entry in /etc/name_to_sysnum"
      /usr/bin/logger "K28SEOS: There are no more empty slots left in there so SEOS_syscall can't load"
      /usr/bin/logger "K28SEOS: Aborting K28SEOS"
      return 1
    fi
  else
      echo ""
      echo "K28SEOS: ERROR, AccessControl must use an entry in /etc/name_to_sysnum"
      echo "            /etc/name_to_sysnum does not exist on your system."
      echo "            Aborting K28SEOS"
      /usr/bin/logger "K28SEOS: ERROR, AccessControl must use an entry in /etc/name_to_sysnum."
      /usr/bin/logger "K28SEOS: /etc/name_to_sysnum does NOT exist on your system."
      /usr/bin/logger "K28SEOS: Aborting K28SEOS"
      return 1
  fi
  return 0
}

# insert the system call into /etc/name_to_sysnum in Solaris.
Solaris_sysnum()
{
        if [ -f /etc/name_to_sysnum ]
        then
                output=`grep SEOS_syscall /etc/name_to_sysnum`
                if [ "$?" != 0 ]
                then
                        /bin/rm -f /etc/name_to_sysnum.bak
                        /bin/cp /etc/name_to_sysnum /etc/name_to_sysnum.bak
                        echo "SEOS_syscall $syscall_number" >> /etc/name_to_sysnum
			sync;sync
                        return 0
                fi
        else
            echo ""
            echo "K28SEOS: ERROR: AccessControl must use an entry in /etc/name_to_sysnum"
            echo "K28SEOS: /etc/name_to_sysnum does not exist on your system."
            echo "K28SEOS: Aborting K28SEOS"
            /usr/bin/logger "K28SEOS: ERROR, AccessControl must use an entry in /etc/name_to_sysnum"
            /usr/bin/logger "K28SEOS: /etc/name_to_sysnum does not exist on your system."
            /usr/bin/logger "K28SEOS: Aborting K28SEOS"
            return 1
        fi
        return 0
}


# Remove SEOS_syscall entry from /etc/name_to_sysnum
Remove_from_sysnum()
{
	if [ -f /etc/name_to_sysnum ]; then
		output=`grep SEOS_syscall /etc/name_to_sysnum`
		if [ "$?" = 0 ]; then
			/bin/cp /etc/name_to_sysnum /etc/name_to_sysnum.bak
			grep -v "^SEOS_syscall" /etc/name_to_sysnum.bak > /etc/name_to_sysnum
			/bin/rm -f /etc/name_to_sysnum.bak
			sync
			sync
		fi
	fi
	return 0
}

#
# If Solaris 10 none global zone or ioctl usage then do nothing
#
OS_minver=`uname -r | awk -F. '{print $2}'`
my_zonename="global"

if [ $OS_minver -ge 10 ]; then
     my_zonename="`zonename`"
fi
if [ "$my_zonename" != "global" ]; then
     exit 0
fi
if [ -f $SEOSDIR/bin/seini ]; then
     use_ioctl=`$SEOSDIR/bin/seini -f SEOS_syscall.SEOS_use_ioctl` >/dev/null 2>&1
     if [ "$use_ioctl" -ne "0" ]; then
         exit 0
     fi
fi

#
# If X86 Solaris then remove SEOS_syscall entry from /etc/name_to_sysnum
#
isSPARC=`uname -p`
if [ $isSPARC != "sparc" ]; then
    Remove_from_sysnum
    exit 0
fi

case "$1" in
'start')
	;;

'stop')
    if [ -d $SEOSDIR/bin ]
    then
        if grep "^SEOS_syscall" /etc/name_to_sysnum >/dev/null 2>&1; then
	    Check_for_dup_sysnum # B521906
        else
            Check_for_sysnum
            if [ $? -eq 1 ]
            then
                exit 1
            fi
            Solaris_sysnum
            if [ $? -eq 1 ]
            then
                exit 1
            fi
        fi
    fi
    ;;

*)
	;;
esac
exit 0
