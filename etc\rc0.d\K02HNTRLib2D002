#!/sbin/sh
#
# Hitachi NwOP Trace Library 2 - Monitor daemon
# --  start and stop script            Aug 21st 1998
#                            last mod. Oct 01st 2004
#

# program directory
BIN_PATH=/opt/hitachi/HNTRLib2/bin
SPOOL_PATH=/opt/hitachi/HNTRLib2/spool
#DEBUG_FILE=/tmp/HNTR2.txt			#340E04

#----------------------------------------------
#  script
#
case $1 in
'start')
	if [ -x $BIN_PATH/hntr2mon ]
	then
		echo "Start HNTRLib2 monitor daemon"
#		date >> $DEBUG_FILE										#340E04
#		echo "Start HNTRLib2 monitor daemon" >> $DEBUG_FILE		#340E04
		$BIN_PATH/hntr2mon -d > /dev/null 2> $SPOOL_PATH/hntr2mon.err	#370B10
		chmod 644 $SPOOL_PATH/hntr2mon.err 2> /dev/null	#360201
	fi
	;;

'stop')
	if [ -x $BIN_PATH/hntr2kill ]
	then
		echo "Stop HNTRLib2 monitor daemon"
#		date >> $DEBUG_FILE										#340E04
#		echo "Stop HNTRLib2 monitor daemon" >> $DEBUG_FILE		#340E04
		$BIN_PATH/hntr2kill > /dev/null 2> /dev/null
	fi
	;;

*)
	echo "usage: $0 {start|stop}"
	;;
esac
