import struct
import os
from datetime import datetime

# Hardcoded wtmpx file path
file_path = '/mnt/d/project_nishiro/PCTSAPPCDOM1_20250524_023203/varadm/wtmpx'  # Change accordingly

def parse_wtmpx(file_path):
    fmt = '32s4s32siH2xII372x'  # Padded for Solaris version without hostname
    record_size = struct.calcsize(fmt)

    print(f"[*] Using record size: {record_size} bytes")

    if not os.path.exists(file_path):
        print(f"[!] File not found at {file_path}")
        return

    file_size = os.path.getsize(file_path)
    if file_size % record_size != 0:
        print(f"[!] Warning: File size {file_size} is not a multiple of record size {record_size}")
    
    print(f"[*] Total records detected: {file_size // record_size}\n")

    with open(file_path, 'rb') as f:
        record_num = 0
        while chunk := f.read(record_size):
            record_num += 1
            try:
                data = struct.unpack(fmt, chunk)

                username = data[0].decode('utf-8', errors='ignore').strip('\x00')
                line = data[2].decode('utf-8', errors='ignore').strip('\x00')
                pid = data[3]
                type_code = data[4]
                tv_sec = data[5]
                tv_usec = data[6]

                timestamp = datetime.utcfromtimestamp(tv_sec).strftime('%Y-%m-%d %H:%M:%S')

                print(f"[Record {record_num}] {timestamp} | User: {username} | TTY: {line} | PID: {pid} | Type: {type_code}")

            except Exception as e:
                print(f"[!] Error parsing record {record_num}: {e}")
                continue


if __name__ == '__main__':
    parse_wtmpx(file_path)
