import struct
import os
import re
import ipaddress
from datetime import datetime, timezone

# Hardcoded wtmpx file path
file_path = 'varadm/wtmpx'  # Change accordingly

def get_type_name(type_code):
    """Convert type code to human readable name"""
    return {
        0: 'EMPTY',
        1: 'RUN_LVL',
        2: 'BOOT_TIME',
        3: 'NEW_TIME',
        4: 'OLD_TIME',
        5: 'INIT_PROCESS',
        6: 'LOGIN_PROCESS',
        7: 'USER_PROCESS',
        8: 'DEAD_PROCESS',
        9: 'ACCOUNTING'
    }.get(type_code, f'UNKNOWN({type_code})')

def is_external_ip(ip_str):
    """
    Efficiently check if an IP address is external (public).
    Uses fast string operations before falling back to ipaddress module.
    """
    if not ip_str or ip_str.isspace():
        return False

    # Fast string-based checks for common private ranges
    # This avoids the overhead of ipaddress parsing for most cases
    if ip_str.startswith(('10.', '192.168.', '127.')):
        return False
    elif ip_str.startswith('172.'):
        # Check if it's in **********/12 range (172.16-31.x.x)
        try:
            second_octet = int(ip_str.split('.')[1])
            if 16 <= second_octet <= 31:
                return False
        except (ValueError, IndexError):
            pass
    elif ip_str.startswith(('169.254.', '224.', '225.', '226.', '227.', '228.', '229.', '230.', '231.', '232.', '233.', '234.', '235.', '236.', '237.', '238.', '239.')):
        return False  # Link-local and multicast

    # For edge cases, use ipaddress module
    try:
        ip = ipaddress.ip_address(ip_str)
        return ip.is_global
    except ValueError:
        return False  # Invalid IP format

def extract_ip_from_hostname(hostname):
    """Extract IP address from hostname string using regex"""
    # Simple regex for IPv4 addresses
    ip_pattern = r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'
    match = re.search(ip_pattern, hostname)
    return match.group(0) if match else hostname if hostname else ""

def parse_wtmpx(file_path, output_file='wtmpx_output.txt'):
    # Solaris wtmpx format (big-endian, 372 bytes total)
    # Based on the blog post: '>32s 4s 32s i H H H b b I I I 5I H 257s b'
    # Let's use the exact format from the blog post
    fmt = '>32s 4s 32s i H H H b b I I I 5I H 257s b'
    record_size = 372

    print(f"[*] Using record size: {record_size} bytes")

    if not os.path.exists(file_path):
        print(f"[!] File not found at {file_path}")
        return

    file_size = os.path.getsize(file_path)
    if file_size % record_size != 0:
        print(f"[!] Warning: File size {file_size} is not a multiple of record size {record_size}")

    print(f"[*] Total records detected: {file_size // record_size}")
    print(f"[*] Output will be saved to: {output_file}\n")

    # Track external IPs for summary
    external_ips = set()
    external_ip_records = []

    with open(file_path, 'rb') as f, open(output_file, 'w', encoding='utf-8') as out_file:
        record_num = 0
        valid_records = 0

        # Write header to output file
        out_file.write("# Solaris WTMPX Log Analysis\n")
        out_file.write(f"# Generated from: {file_path}\n")
        out_file.write(f"# Total records: {file_size // record_size}\n")
        out_file.write("# Format: [Record#] Timestamp | User | TTY | PID | Type | Host\n\n")

        while chunk := f.read(record_size):
            if len(chunk) < record_size:
                error_msg = f"[!] Incomplete record {record_num + 1}, only {len(chunk)} bytes"
                print(error_msg)
                out_file.write(error_msg + "\n")
                break

            record_num += 1
            try:
                # Pad the chunk to exactly 372 bytes if needed
                if len(chunk) < record_size:
                    chunk = chunk + b'\x00' * (record_size - len(chunk))

                data = struct.unpack(fmt, chunk)

                username = data[0].decode('utf-8', errors='ignore').strip('\x00')
                ut_id = data[1].decode('utf-8', errors='ignore').strip('\x00')
                line = data[2].decode('utf-8', errors='ignore').strip('\x00')
                pid = data[3]
                type_code = data[4]
                e_termination = data[5]
                e_exit = data[6]
                # Skip padding byte (data[7])
                # Skip padding byte (data[8])
                tv_sec = data[9]
                tv_usec = data[10]
                session = data[11]
                # Skip pad[5] (data[12-16])
                syslen = data[17]
                hostname_raw = data[18].decode('utf-8', errors='ignore').strip('\x00')
                hostname = hostname_raw[:syslen] if syslen > 0 and syslen <= len(hostname_raw) else hostname_raw

                # Convert timestamp (handle timezone properly)
                if tv_sec > 0:
                    timestamp = datetime.fromtimestamp(tv_sec, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
                else:
                    timestamp = "N/A"

                type_name = get_type_name(type_code)

                # Only show meaningful records
                if type_code != 0 or username or line or hostname:
                    record_line = f"[Record {record_num}] {timestamp} | User: {username:12} | TTY: {line:12} | PID: {pid:8} | Type: {type_name:12} | Host: {hostname}"
                    print(record_line)
                    out_file.write(record_line + "\n")
                    valid_records += 1

                    # Check for external IP addresses
                    if hostname:
                        ip = extract_ip_from_hostname(hostname)
                        if ip and is_external_ip(ip):
                            external_ips.add(ip)
                            external_ip_records.append({
                                'record_num': record_num,
                                'timestamp': timestamp,
                                'username': username,
                                'ip': ip,
                                'type': type_name
                            })

            except Exception as e:
                error_msg = f"[!] Error parsing record {record_num}: {e}"
                print(error_msg)
                out_file.write(error_msg + "\n")
                continue

        # Write summary
        summary_msg = f"\n[*] Parsing complete. Total records processed: {record_num}, Valid records shown: {valid_records}"
        print(summary_msg)
        out_file.write(summary_msg + "\n")

        # External IP Analysis
        if external_ips:
            ext_ip_msg = f"\n[!] EXTERNAL IP ADDRESSES DETECTED: {len(external_ips)} unique IPs"
            print(ext_ip_msg)
            out_file.write(ext_ip_msg + "\n")
            out_file.write("=" * 80 + "\n")

            # Group by IP for better analysis
            ip_summary = {}
            for record in external_ip_records:
                ip = record['ip']
                if ip not in ip_summary:
                    ip_summary[ip] = []
                ip_summary[ip].append(record)

            # Write detailed external IP analysis
            for ip in sorted(external_ips):
                records = ip_summary[ip]
                ip_header = f"\n[EXTERNAL IP] {ip} - {len(records)} connection(s)"
                print(ip_header)
                out_file.write(ip_header + "\n")

                for record in records:
                    detail_line = f"  Record {record['record_num']}: {record['timestamp']} | User: {record['username']} | Type: {record['type']}"
                    print(detail_line)
                    out_file.write(detail_line + "\n")
        else:
            no_ext_msg = "\n[*] No external IP addresses detected in the logs."
            print(no_ext_msg)
            out_file.write(no_ext_msg + "\n")

    print(f"\n[*] Analysis complete. Full output saved to: {output_file}")


if __name__ == '__main__':
    parse_wtmpx(file_path)
