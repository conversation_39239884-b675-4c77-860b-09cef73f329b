import struct
import os
from datetime import datetime, timezone

# Hardcoded wtmpx file path
file_path = 'varadm/wtmpx'  # Change accordingly

def get_type_name(type_code):
    """Convert type code to human readable name"""
    return {
        0: 'EMPTY',
        1: 'RUN_LVL',
        2: 'BOOT_TIME',
        3: 'NEW_TIME',
        4: 'OLD_TIME',
        5: 'INIT_PROCESS',
        6: 'LOGIN_PROCESS',
        7: 'USER_PROCESS',
        8: 'DEAD_PROCESS',
        9: 'ACCOUNTING'
    }.get(type_code, f'UNKNOWN({type_code})')

def parse_wtmpx(file_path):
    # Solaris wtmpx format (big-endian, 372 bytes total)
    # Based on the blog post: '>32s 4s 32s i H H H b b I I I 5I H 257s b'
    # Let's use the exact format from the blog post
    fmt = '>32s 4s 32s i H H H b b I I I 5I H 257s b'
    record_size = 372

    print(f"[*] Using record size: {record_size} bytes")

    if not os.path.exists(file_path):
        print(f"[!] File not found at {file_path}")
        return

    file_size = os.path.getsize(file_path)
    if file_size % record_size != 0:
        print(f"[!] Warning: File size {file_size} is not a multiple of record size {record_size}")

    print(f"[*] Total records detected: {file_size // record_size}\n")

    with open(file_path, 'rb') as f:
        record_num = 0
        valid_records = 0
        while chunk := f.read(record_size):
            if len(chunk) < record_size:
                print(f"[!] Incomplete record {record_num + 1}, only {len(chunk)} bytes")
                break

            record_num += 1
            try:
                # Pad the chunk to exactly 372 bytes if needed
                if len(chunk) < record_size:
                    chunk = chunk + b'\x00' * (record_size - len(chunk))

                data = struct.unpack(fmt, chunk)



                username = data[0].decode('utf-8', errors='ignore').strip('\x00')
                ut_id = data[1].decode('utf-8', errors='ignore').strip('\x00')
                line = data[2].decode('utf-8', errors='ignore').strip('\x00')
                pid = data[3]
                type_code = data[4]
                e_termination = data[5]
                e_exit = data[6]
                # Skip padding byte (data[7])
                # Skip padding byte (data[8])
                tv_sec = data[9]
                tv_usec = data[10]
                session = data[11]
                # Skip pad[5] (data[12-16])
                syslen = data[17]
                hostname_raw = data[18].decode('utf-8', errors='ignore').strip('\x00')
                hostname = hostname_raw[:syslen] if syslen > 0 and syslen <= len(hostname_raw) else hostname_raw

                # Convert timestamp (handle timezone properly)
                if tv_sec > 0:
                    timestamp = datetime.fromtimestamp(tv_sec, tz=timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')
                else:
                    timestamp = "N/A"

                type_name = get_type_name(type_code)

                # Only show meaningful records
                if type_code != 0 or username or line or hostname:
                    print(f"[Record {record_num}] {timestamp} | User: {username:12} | TTY: {line:12} | PID: {pid:8} | Type: {type_name:12} | Host: {hostname}")
                    valid_records += 1

            except Exception as e:
                print(f"[!] Error parsing record {record_num}: {e}")
                continue

        print(f"\n[*] Parsing complete. Total records processed: {record_num}, Valid records shown: {valid_records}")


if __name__ == '__main__':
    parse_wtmpx(file_path)
