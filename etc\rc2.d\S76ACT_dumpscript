#!/bin/sh
#
# Copyright (c) 1998, 2004, 2012, Oracle and/or its affiliates. All rights reserved.
#
#	<PERSON>ript to Automatically run ACT against a crash dump creating
#	a file called act.<CRASH-No> in the crash dump directory
#
ACTDIR="/usr/lib/act"
export ACTDIR

#
# Assuming we have dumpadm and that crash dumps are enabled
#
if [ -r /etc/dumpadm.conf ]; then
    . /etc/dumpadm.conf
else
    echo "WARNING: /etc/dumpadm.conf is missing or unreadable" >& 2
    exit 1
fi

#
# Background the time-consuming stuff
#
(
    pid=`/bin/pgrep savecore`
    if [ ! -z "$pid" ]; then
	/usr/proc/bin/pwait $pid >/dev/null 2>&1
    fi
    if [ -f $DUMPADM_SAVDIR/bounds ]; then
	CRASHNO=`/bin/cat $DUMPADM_SAVDIR/bounds`
	CRASHNO=`expr $CRASHNO - 1`
	if [ ! -f $DUMPADM_SAVDIR/act.$CRASHNO ]; then
	    echo "Running ACT on Crash dump $CRASHNO"
	    $ACTDIR/bin/act -n $DUMPADM_SAVDIR/unix.$CRASHNO \
	      -d $DUMPADM_SAVDIR/vmcore.$CRASHNO > \
	      $DUMPADM_SAVDIR/act.$CRASHNO
	fi
    fi
) &
