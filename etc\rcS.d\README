#
# Copyright (c) 1993, 2016, Oracle and/or its affiliates. All rights reserved.
#

NOTE: This directory contains legacy initialization and termination
scripts for managing services.  The preferred method of service
management is via the Service Management Facility; to read more about
SMF, consult smf(7).

For a general discussion of the mechanism used to invoke these scripts
see the file /etc/init.d/README.

After all its dependencies have been satisfied, the start method of the
SMF major milestone "svc:/milestone/single-user:default" executes each
'S' script within /etc/rcS.d/ with the argument 'start'.  Any warnings,
errors, or output from these scripts are logged to the file:

/var/svc/log/milestone-single-user:default.log

If the system is changing from a higher run-level (for example, through
an invocation of "init S"), SMF executes all 'K' scripts within
/etc/rcS.d/ with the argument 'stop'. Any warnings, errors, or output
from these scripts are logged to the file:

/var/svc/log/rcS.log

Scripts in /etc/rcS.d/ may assume the following:

	Enough network plumbing has been done to NFS mount /usr/.

	All system-supplied device file names have been established.

	The environment variable _INIT_RECONFIG is set if this is
	a reconfiguration boot.

	The base system mounts have been performed, and the file
	systems are read/write if so specified.  These are:

                /
                /usr
                /proc
                /dev/fd
                /tmp
                /var
                /var/adm
                /system/volatile
                /dev
                /devices
                /etc

For a full list of services brought online before scripts are run, see
the output of "svcs -l svc:/milestone/single-user:default".
