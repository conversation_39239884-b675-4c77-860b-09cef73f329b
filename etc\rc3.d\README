 Copyright (c) 1993, 2016, Oracle and/or its affiliates. All rights reserved.

NOTE: This directory contains legacy initialization and termination
scripts for managing services.  The preferred method of service
management is via the Service Management Facility; to read more about
SMF, consult smf(7).

For a general discussion of the mechanism used to invoke these scripts
see the file /etc/init.d/README.

When moving to run-level 3, via an "init 3" invocation or the SMF major
milestone "svc:/milestone/multi-user-server:default", /usr/sbin/rc3 executes
each 'K' script within /etc/rc3.d/ with the argument 'stop', followed by
each 'S' script within /etc/rc3.d/ with the argument 'start'.   All
start scripts in the directories /etc/rcS.d/ and /etc/rc2.d/ will have
been run as part of the earlier major milestones.  Any warnings, errors,
or output from the scripts in /etc/rc3.d/ are logged to the file:

/var/svc/log/milestone-multi-user-server:default.log

Scripts in /etc/rc3.d/ may assume the following:

	The NFS server, if enabled, is running and shared NFS filesystems
	are exported.

For a full list of services brought online before scripts are run, see
the output of "svcs -l svc:/milestone/multi-user-server:default".
