#ident  "%Z%%M% %I%     %E% SMI"
#
# Copyright 2007 Sun Microsystems, Inc.  All rights reserved.
# Use is subject to license terms.
#
# The root crontab should be used to perform accounting data collection.
#
#
10 3 * * * /usr/sbin/logadm
30 3 * * * [ -x /usr/lib/gss/gsscred_clean ] && /usr/lib/gss/gsscred_clean

0,5,10,15,20,25,30,35,40,45,50,55 * * * * /var/sysadm/script/health.db

#00 00 * * * /mnt/iostat/run_iostat_sol.sh

59 23 * * * /var/sysadm/audit/seauditLOG.sh
58 2 * * * /var/sysadm/script/ExtractLogs.sh

12 04 * * * /var/sysadm/script/ftp_savelog.sh

#####For Disk Path Monitoring
0,15,30,45 * * * * /var/sysadm/script/DRP/Pair_Display_GAD_OVIO.sh

#####For Hitachi SI#####
#30 12 * * 1-5 /var/sysadm/script/cgSISyncSuspendCron_GW_TC_CTSApp.sh
#40 14 * * 1-5 /var/sysadm/script/cgSISyncSuspendCron_GW_TC_CTSApp.sh
#00 16 * * 1-5 /var/sysadm/script/cgSISyncSuspendCron_GW_TC_CTSApp.sh
#05 18 * * 1-5 /var/sysadm/script/cgSISyncSuspendCron_GW_TC_CTSApp.sh
#15 19 * * 1-5 /var/sysadm/script/cgSISyncSuspendCron_GW_TC_CTSApp.sh
#50 01 * * 2-6 /var/sysadm/script/cgSISyncSuspendCron_GW_TC_CTSApp.sh
05 05 * * 2-6 /var/sysadm/script/cgSISyncSuspendCron_GW_TC_CTSApp.sh
#####For Hitachi SI####

##########################
# For RSA Analytics
##########################
#05,15,25,35,45,55 * * * * /var/sysadm/script/nilogger.sh

#####For HDLM Path monitoring
0,15,30,45 * * * * /var/sysadm/script/chkHDLMPath.sh


# Start of lines added by ha-cluster/system/core
20 4 * * 0 /usr/cluster/lib/sc/newcleventlog /var/cluster/logs/eventlog || date
20 4 * * 0 /usr/cluster/lib/sc/newcleventlog /var/cluster/logs/DS || date
20 4 * * 0 /usr/cluster/lib/sc/newcleventlog /var/cluster/logs/commandlog || date
# End of lines added by ha-cluster/system/core

######## DC Reloc SI Timing ################

## CTS ##
#15 04 * * 0 /var/sysadm/script/DCReloc_cgSISyncSuspendCron_CTSAPPDBOS.sh
#30 04 * * 0 /var/sysadm/script/DCReloc_cgSISyncSuspendCron_GW_TC_CTSApp.sh

######## End Proposed Timing ################

0 * * * * /usr/sbin/audit -n
