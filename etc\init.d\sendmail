#!/usr/sbin/sh
#
#
#
# Copyright (c) 2004, 2011, Oracle and/or its affiliates. All rights reserved.
#

# This service is managed by smf(5).  Thus, this script provides
# compatibility with previously documented init.d script behaviour.

case "$1" in
'restart')
	# The previous init.d/sendmail restart behaviour matches
	# the smf(5) 'refresh' semantics.

	svcadm refresh network/smtp:sendmail
	svcadm refresh network/sendmail-client:default
	;;

'start')
	svcadm enable -t network/smtp:sendmail
	svcadm enable -t network/sendmail-client:default
        ;;

'stop')
	svcadm disable -t network/smtp:sendmail
	svcadm disable -t network/sendmail-client:default
        ;;

*)
        echo "Usage: $0 { start | stop | restart }"
        exit 1
        ;;
esac
