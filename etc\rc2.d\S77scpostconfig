#!/bin/sh
#
# Copyright (c) 2004, 2018, Oracle and/or its affiliates. All rights reserved.
#

SC_CLUSTER=/usr/cluster/bin/cluster
SC_QUORUMCONFIG=/usr/cluster/lib/sc/scquorumconfig
POSTCONFIGFILE=/etc/cluster/ccr/global/postconfig
SCLIB=/usr/cluster/lib/sc
GREP=/usr/bin/grep
NAWK=/usr/bin/nawk
SORT=/bin/sort
WC=/bin/wc

tmp_logfile="/tmp/postconfig.log.$$"

get_installmode()
{
        (
                LC_ALL=C; export LC_ALL
                mode=`${SC_CLUSTER} show -t global | sed -n 's/^  installmode:[   ]*\([^ ]*\).*/\1/p'`
                if [ -n "${mode}" ] && [ "${mode}" = "enabled" ]
		then
                        echo 1
                else
                        echo 0
                fi
        )

        return 0
}

# test whether we are a cluster and exit if not a cluster
/usr/sbin/clinfo > /dev/null 2>&1
if [ $? -ne 0 ]
then
	exit 0
fi

nodename=`/sbin/uname -n`
tasks=`${GREP} '^task\.' ${POSTCONFIGFILE} 2>/dev/null | ${NAWK} -F. '{print $2}' | ${SORT} -u`

for task in ${tasks}; do
	case "${task}" in
	'quorum')
		installmode=`get_installmode`
		if [ ${installmode} -eq 1 ]
		then
			# Create quorum devices in 2 node clusters and do only
			# cluster initialization in >2 or 1 node clusters.
			nodecount=`${GREP} '^task.' ${POSTCONFIGFILE} | ${NAWK} -F. '{ if ($2 == "quorum") print $3 }' | ${WC} -l`
			if [ ${nodecount} -eq 2 ]
			then
        			${SC_QUORUMCONFIG} > ${tmp_logfile} 2>&1
				exit_status=$?
				if [ ${exit_status} -eq 1 ]
				then
					/usr/bin/logger -p daemon.err -t SCPOSTCONFIG -f ${tmp_logfile}
					# SCMSGS
					# @explanation
					# The attempt to automatically
					# configure quorum disks failed on one
					# or more nodes. The last node to join
					# the cluster is supposed to configure
					# quorum. However, other nodes also
					# perform registration tasks when they
					# join the cluster. This message
					# indicates that one or more of these
					# attempts failed, which prevented the
					# last node from configuring quorum.
					# @user_action
					# Manually configure quorum disks and
					# initialize the cluster by using
					# clsetup after all nodes
					# have joined the cluster.
					${SCLIB}/scds_syslog -p error -t SCPOSTCONFIG -m "The quorum configuration task encountered a problem on node ${nodename}, manual configuration by using clsetup(1CL) might be necessary"
					cat ${tmp_logfile}
					echo "The quorum configuration task encountered a problem on node ${nodename}, manual configuration by using clsetup(1CL) might be necessary"

				elif [ ${exit_status} -eq 0 ]
				then
					/usr/bin/logger -p daemon.notice -t SCPOSTCONFIG -f ${tmp_logfile}
					# SCMSGS
					# @explanation
					# Quorum disk was configured and the
					# cluster was initialized on the last
					# of the 2 nodes to join the cluster.
					# @user_action
					# None.
					${SCLIB}/scds_syslog -p notice -t SCPOSTCONFIG -m "The quorum configuration task succeeded on node ${nodename}"
					cat ${tmp_logfile}
					echo "The quorum configuration task succeeded on node ${nodename}"

				fi
			else
        			${SC_QUORUMCONFIG} -i > ${tmp_logfile} 2>&1
				exit_status=$?
				if [ ${exit_status} -eq 1 ]
				then
					/usr/bin/logger -p daemon.err -t SCPOSTCONFIG -f ${tmp_logfile}
					# SCMSGS
					# @explanation
					# The attempt to automatically reset
					# cluster installmode or quorum votes
					# failed on one or more nodes. The
					# last node to join the cluster is
					# supposed to perform these tasks.
					# However, other nodes also perform
					# registration tasks when they join
					# the cluster. This message indicates
					# that one or more of these attempts
					# failed, which prevented the last
					# node from initializing the cluster.
					# @user_action
					# Run clsetup after all
					# nodes have joined the cluster, to
					# complete post-installation setup.
					${SCLIB}/scds_syslog -p error -t SCPOSTCONFIG -m "Cluster initialization encountered a problem on node ${nodename}, manual initialization by using clsetup(1CL) might be necessary"
					cat ${tmp_logfile}
					echo "Cluster initialization encountered a problem on node ${nodename}, manual initialization by using clsetup(1CL) might be necessary"

				fi
			fi
			rm -f ${tmp_logfile}
		fi
        	;;

	'security')
		# This is handled after rgm is up in rgm starter script
       		;;

	'uaquorum')
		# This is only for auto install and is in the sc-ai-config service.
       		;;

	*)
		# SCMSGS
		# @explanation
		# A task was found on the postconfiguration CCR file
		# that was not understood. This probably indicated
		# that the CCR file was corrupt.
		# @user_action
		# Manually configure quorum disks and initialize the
		# cluster or set the resource_security property by using clsetup after all nodes
		# have joined the cluster.
		${SCLIB}/scds_syslog -p error -t SCPOSTCONFIG -m "Unsupported task ${task} in ${POSTCONFIGFILE}"
       		;;
	esac
done
