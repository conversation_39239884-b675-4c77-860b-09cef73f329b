STATE          STIME               FMRI
legacy_run     2025-04-19T12:20:09 lrc:/etc/rc2_d/S68SEOS
legacy_run     2025-04-19T12:20:09 lrc:/etc/rc2_d/S72sc_update_hosts
legacy_run     2025-04-19T12:20:09 lrc:/etc/rc2_d/S72sc_update_ntp
legacy_run     2025-04-19T12:20:09 lrc:/etc/rc2_d/S76ACT_dumpscript
legacy_run     2025-04-19T12:20:09 lrc:/etc/rc2_d/S77scpostconfig
legacy_run     2025-04-19T12:20:10 lrc:/etc/rc2_d/S95SUNWmd_binddevs
legacy_run     2025-04-19T12:20:20 lrc:/etc/rc2_d/S99sneep
legacy_run     2025-04-19T12:20:30 lrc:/etc/rc3_d/S73ovpa
legacy_run     2025-04-19T12:20:31 lrc:/etc/rc3_d/S73pctl
legacy_run     2025-04-19T12:20:35 lrc:/etc/rc3_d/S95networker
legacy_run     2025-04-19T12:20:35 lrc:/etc/rc3_d/S98HNTRLib2D002
legacy_run     2025-04-19T12:20:57 lrc:/etc/rc3_d/S99OVCtrl
disabled       2025-04-19T12:18:44 svc:/system/device/mpxio-upgrade:default
disabled       2025-04-19T12:18:44 svc:/system/labeld:default
disabled       2025-04-19T12:18:45 svc:/network/dhcp/client:default
disabled       2025-04-19T12:18:45 svc:/network/dns/client:default
disabled       2025-04-19T12:18:45 svc:/network/firewall:default
disabled       2025-04-19T12:18:45 svc:/network/ipsec/ike:default
disabled       2025-04-19T12:18:45 svc:/network/ipsec/ike:ikev2
disabled       2025-04-19T12:18:45 svc:/network/ipsec/manual-key:default
disabled       2025-04-19T12:18:45 svc:/network/nis/client:default
disabled       2025-04-19T12:18:45 svc:/network/nis/domain:default
disabled       2025-04-19T12:18:46 svc:/application/cups/scheduler:default
disabled       2025-04-19T12:18:46 svc:/network/inetd:default
disabled       2025-04-19T12:18:46 svc:/network/ldap/client:default
disabled       2025-04-19T12:18:46 svc:/system/identity:cert
disabled       2025-04-19T12:18:47 svc:/system/mdmonitor:default
disabled       2025-04-19T12:18:48 svc:/application/management/common-agent-container-1:default
disabled       2025-04-19T12:18:48 svc:/network/ldap/identity:openldap
disabled       2025-04-19T12:18:48 svc:/network/ldap/server:openldap
disabled       2025-04-19T12:18:48 svc:/network/nfs/cbd:default
disabled       2025-04-19T12:18:48 svc:/network/nfs/client:default
disabled       2025-04-19T12:18:48 svc:/network/nfs/mapid:default
disabled       2025-04-19T12:18:48 svc:/network/nfs/nlockmgr:default
disabled       2025-04-19T12:18:48 svc:/network/nfs/status:default
disabled       2025-04-19T12:18:48 svc:/network/rpc/gss:default
disabled       2025-04-19T12:18:48 svc:/network/rpc/keyserv:default
disabled       2025-04-19T12:18:48 svc:/network/smb/client:default
disabled       2025-04-19T12:18:48 svc:/system/filesystem/autofs:default
disabled       2025-04-19T12:18:48 svc:/system/idmap:default
disabled       2025-04-19T12:18:48 svc:/system/kerberos/install:default
disabled       2025-04-19T12:18:48 svc:/system/rcap:default
disabled       2025-04-19T12:18:48 svc:/system/system-log:rsyslog
disabled       2025-04-19T12:18:50 svc:/application/pkg/depot:default
disabled       2025-04-19T12:18:50 svc:/application/pkg/dynamic-mirror:default
disabled       2025-04-19T12:18:50 svc:/application/pkg/mirror:default
disabled       2025-04-19T12:18:50 svc:/application/pkg/repositories-setup:default
disabled       2025-04-19T12:18:50 svc:/application/pkg/server:default
disabled       2025-04-19T12:18:50 svc:/application/pkg/system-repository:default
disabled       2025-04-19T12:18:50 svc:/application/pkg/zones-proxyd:default
disabled       2025-04-19T12:18:50 svc:/application/security/tcsd:default
disabled       2025-04-19T12:18:50 svc:/application/security/tpmfod:default
disabled       2025-04-19T12:18:50 svc:/network/dns/multicast:default
disabled       2025-04-19T12:18:50 svc:/network/ntp:monitor
disabled       2025-04-19T12:18:51 svc:/network/dhcp/relay:ipv4
disabled       2025-04-19T12:18:51 svc:/network/dhcp/relay:ipv6
disabled       2025-04-19T12:18:51 svc:/network/dhcp/server:ipv4
disabled       2025-04-19T12:18:51 svc:/network/dhcp/server:ipv6
disabled       2025-04-19T12:18:51 svc:/network/diagnostics:default
disabled       2025-04-19T12:18:51 svc:/network/dlmp:default
disabled       2025-04-19T12:18:51 svc:/network/dns/server:default
disabled       2025-04-19T12:18:51 svc:/network/firewall/ftp-proxy:default
disabled       2025-04-19T12:18:51 svc:/network/firewall/pflog:default
disabled       2025-04-19T12:18:51 svc:/network/ftp:default
disabled       2025-04-19T12:18:51 svc:/network/nfs/server:default
disabled       2025-04-19T12:18:51 svc:/system/filesystem/reparse:default
disabled       2025-04-19T12:18:52 svc:/network/http:apache24
disabled       2025-04-19T12:18:52 svc:/network/ipmievd:default
disabled       2025-04-19T12:18:52 svc:/network/ipsec/policy:logger
disabled       2025-04-19T12:18:52 svc:/network/loadbalancer/ilb:default
disabled       2025-04-19T12:18:52 svc:/network/routing/legacy-routing:ipv4
disabled       2025-04-19T12:18:52 svc:/network/routing/legacy-routing:ipv6
disabled       2025-04-19T12:18:52 svc:/network/routing/ripng:default
disabled       2025-04-19T12:18:52 svc:/network/routing/route:default
disabled       2025-04-19T12:18:52 svc:/network/security/kadmin:default
disabled       2025-04-19T12:18:52 svc:/network/security/krb5_prop:default
disabled       2025-04-19T12:18:52 svc:/network/security/krb5kdc:default
disabled       2025-04-19T12:18:52 svc:/network/sendmail-client:default
disabled       2025-04-19T12:18:52 svc:/network/smb/server:default
disabled       2025-04-19T12:18:52 svc:/network/socket-filter:pf_divert
disabled       2025-04-19T12:18:52 svc:/security/seosd:default
disabled       2025-04-19T12:18:52 svc:/system/apache-stats-24:default
disabled       2025-04-19T12:18:52 svc:/system/cluster/cacao-install:default
disabled       2025-04-19T12:18:52 svc:/system/cluster/cacao-uninstall:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/dsconfig-wizard-install:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/manager-wls:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/manager:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/sc-ai-config:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/sc-ovm-config:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/sc-sua-boot:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/sc_ifconfig_proxy:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/sc_ng_zones:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/sc_restarter:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/sc_zc_member:default
disabled       2025-04-19T12:18:53 svc:/system/cluster/svc_private_network:default
disabled       2025-04-19T12:18:53 svc:/system/extended-accounting:process
disabled       2025-04-19T12:18:54 svc:/system/consadm:default
disabled       2025-04-19T12:18:54 svc:/system/console-login:terma
disabled       2025-04-19T12:18:54 svc:/system/console-login:termb
disabled       2025-04-19T12:18:54 svc:/system/device/allocate:default
disabled       2025-04-19T12:18:54 svc:/system/extended-accounting:flow
disabled       2025-04-19T12:18:54 svc:/system/extended-accounting:net
disabled       2025-04-19T12:18:54 svc:/system/extended-accounting:task
disabled       2025-04-19T12:18:55 svc:/system/cluster/bootcluster:default
disabled       2025-04-19T12:18:55 svc:/system/cluster/clexecd:default
disabled       2025-04-19T12:18:55 svc:/system/cluster/initdid:default
disabled       2025-04-19T12:18:55 svc:/system/cluster/sc_failfast:default
disabled       2025-04-19T12:18:55 svc:/system/filesystem/rmvolmgr:default
disabled       2025-04-19T12:18:55 svc:/system/filesystem/uvfs:default
disabled       2025-04-19T12:18:55 svc:/system/fm/notify-params:default
disabled       2025-04-19T12:18:55 svc:/system/fm/snmp-notify:default
disabled       2025-04-19T12:18:55 svc:/system/install/server:default
disabled       2025-04-19T12:18:55 svc:/system/power:default
disabled       2025-04-19T12:18:56 svc:/system/cluster/cl-svc-enable:default
disabled       2025-04-19T12:18:56 svc:/system/cluster/cl_execd:default
disabled       2025-04-19T12:18:56 svc:/system/cluster/gdevsync:default
disabled       2025-04-19T12:18:56 svc:/system/cluster/globaldevices:default
disabled       2025-04-19T12:18:56 svc:/system/cluster/ql_upgrade:default
disabled       2025-04-19T12:18:56 svc:/system/cluster/zc_cmd_log_replay:default
disabled       2025-04-19T12:18:56 svc:/system/labeld:change
disabled       2025-04-19T12:18:56 svc:/system/labeld:init
disabled       2025-04-19T12:18:56 svc:/system/memory-reserve:osm
disabled       2025-04-19T12:18:56 svc:/system/memory-reserve:zones
disabled       2025-04-19T12:18:56 svc:/system/pkcs11:metaslot
disabled       2025-04-19T12:18:56 svc:/system/pkcs11:softtoken
disabled       2025-04-19T12:18:56 svc:/system/pkcs11:tpm
disabled       2025-04-19T12:18:56 svc:/system/pools/dynamic:default
disabled       2025-04-19T12:18:56 svc:/system/rad:remote
disabled       2025-04-19T12:18:56 svc:/system/rds:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/cl-ccra:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/cl-event:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/cl-eventlog:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/cl-svc-cluster-milestone:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/clevent_listenerd:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/clusterdata:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/clzfs_svcd:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/cznetd:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/mountgfs:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/pnm:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/ql_rgm:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/rpc-fed:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/rpc-pmf:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/sc_pmmd:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/sc_zones:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/scdpm:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/scprivipd:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/scqdm:default
disabled       2025-04-19T12:18:57 svc:/system/cluster/scslmclean:default
disabled       2025-04-19T12:18:57 svc:/system/sar:default
disabled       2025-04-19T12:18:57 svc:/system/svc/global:default
disabled       2025-04-19T12:18:57 svc:/system/webui/server:default
disabled       2025-04-19T12:19:13 svc:/application/pkg/sysrepo-cache-compact:default
disabled       2025-04-19T12:19:13 svc:/application/security/compliance:default
disabled       2025-04-19T12:19:13 svc:/system/auto-update:default
disabled       2025-04-19T12:20:02 svc:/system/console-login:vt2
disabled       2025-04-19T12:20:02 svc:/system/console-login:vt3
disabled       2025-04-19T12:20:02 svc:/system/console-login:vt4
disabled       2025-04-19T12:20:02 svc:/system/console-login:vt5
disabled       2025-04-19T12:20:02 svc:/system/console-login:vt6
disabled       2025-04-19T12:20:02 svc:/system/vtdaemon:default
online         2025-04-19T12:18:44 svc:/system/early-manifest-import:default
online         2025-04-19T12:18:44 svc:/system/svc/restarter:default
online         2025-04-19T12:18:46 svc:/milestone/immutable-setup:default
online         2025-04-19T12:18:46 svc:/network/ib/ib-management:default
online         2025-04-19T12:18:46 svc:/network/sctp/congestion-control:cubic
online         2025-04-19T12:18:46 svc:/network/sctp/congestion-control:highspeed
online         2025-04-19T12:18:46 svc:/network/sctp/congestion-control:newreno
online         2025-04-19T12:18:46 svc:/network/sctp/congestion-control:vegas
online         2025-04-19T12:18:46 svc:/network/socket-config:default
online         2025-04-19T12:18:46 svc:/network/tcp/congestion-control:cubic
online         2025-04-19T12:18:46 svc:/network/tcp/congestion-control:dctcp
online         2025-04-19T12:18:46 svc:/network/tcp/congestion-control:highspeed
online         2025-04-19T12:18:46 svc:/network/tcp/congestion-control:newreno
online         2025-04-19T12:18:46 svc:/network/tcp/congestion-control:vegas
online         2025-04-19T12:18:46 svc:/network/tcp/tcpkey:default
online         2025-04-19T12:18:47 svc:/network/ip-interface-management:default
online         2025-04-19T12:18:47 svc:/system/filesystem/root:default
online         2025-04-19T12:18:47 svc:/system/metainit:default
online         2025-04-19T12:18:48 svc:/network/datalink-management:default
online         2025-04-19T12:18:49 svc:/network/ipsec/ipsecalgs:default
online         2025-04-19T12:18:49 svc:/network/loopback:default
online         2025-04-19T12:18:49 svc:/system/timezone:default
online         2025-04-19T12:18:50 svc:/network/ipsec/policy:default
online         2025-04-19T12:18:50 svc:/network/physical:upgrade
online         2025-04-19T12:18:50 svc:/network/smb:default
online         2025-04-19T12:18:50 svc:/system/cryptosvc:default
online         2025-04-19T12:18:50 svc:/system/resource-controls:default
online         2025-04-19T12:18:50 svc:/system/scheduler:default
online         2025-04-19T12:18:51 svc:/network/evs-controller:default
online         2025-04-19T12:18:51 svc:/network/ipmp:default
online         2025-04-19T12:18:51 svc:/network/routing/ndp:default
online         2025-04-19T12:18:54 svc:/system/boot-archive-update:default
online         2025-04-19T12:18:54 svc:/system/boot-archive:default
online         2025-04-19T12:18:54 svc:/system/devchassis:cleanstart
online         2025-04-19T12:18:54 svc:/system/filesystem/usr:default
online         2025-04-19T12:18:55 svc:/system/sysobj:default
online         2025-04-19T12:18:56 svc:/system/pfexec:default
online         2025-04-19T12:18:57 svc:/system/splice:default
online         2025-04-19T12:18:58 svc:/system/cluster/cl_boot_check:default
online         2025-04-19T12:19:06 svc:/system/cluster/scmountdev:default
online         2025-04-19T12:19:06 svc:/system/device/local:default
online         2025-04-19T12:19:11 svc:/platform/sun4v/drd:default
online         2025-04-19T12:19:11 svc:/system/filesystem/minimal:default
online         2025-04-19T12:19:11 svc:/system/metasync:default
online         2025-04-19T12:19:11 svc:/system/rad:local
online         2025-04-19T12:19:11 svc:/system/rmtmpfiles:default
online         2025-04-19T12:19:11 svc:/system/utmp:default
online         2025-04-19T12:19:12 svc:/application/desktop-cache/desktop-mime-cache:default
online         2025-04-19T12:19:12 svc:/application/desktop-cache/docbook-style-xsl-update:default
online         2025-04-19T12:19:12 svc:/application/opengl/ogl-select:default
online         2025-04-19T12:19:12 svc:/ldoms/agents:default
online         2025-04-19T12:19:12 svc:/milestone/unconfig:default
online         2025-04-19T12:19:12 svc:/system/account-policy:default
online         2025-04-19T12:19:12 svc:/system/coreadm:default
online         2025-04-19T12:19:12 svc:/system/environment:init
online         2025-04-19T12:19:12 svc:/system/hotplug:default
online         2025-04-19T12:19:12 svc:/system/logadm-upgrade:default
online         2025-04-19T12:19:12 svc:/system/pkgserv:default
online         2025-04-19T12:19:12 svc:/system/pools:default
online         2025-04-19T12:19:12 svc:/system/resource-mgmt:default
online         2025-04-19T12:19:12 svc:/system/svc/periodic-restarter:default
online         2025-04-19T12:19:12 svc:/system/zones-monitoring:default
online         2025-04-19T12:19:13 svc:/application/desktop-cache/docbook-style-dsssl-update:default
online         2025-04-19T12:19:13 svc:/application/desktop-cache/glib-compile-schemas:default
online         2025-04-19T12:19:13 svc:/system/cluster/scslm:default
online         2025-04-19T12:19:13 svc:/system/colord:default
online         2025-04-19T12:19:13 svc:/system/dbus:default
online         2025-04-19T12:19:13 svc:/system/security/security-extensions:default
online         2025-04-19T12:19:13 svc:/system/sysevent:default
online         2025-04-19T12:19:14 svc:/application/desktop-cache/docbook-dtds-update:default
online         2025-04-19T12:19:14 svc:/application/desktop-cache/gio-module-cache:default
online         2025-04-19T12:19:14 svc:/application/desktop-cache/pixbuf-loaders-installer:default
online         2025-04-19T12:19:17 svc:/application/desktop-cache/gtk3-input-method-cache:default
online         2025-04-19T12:19:17 svc:/application/desktop-cache/input-method-cache:default
online         2025-04-19T12:19:17 svc:/application/font/fc-cache:default
online         2025-04-19T12:19:17 svc:/system/devfsadm:default
online         2025-04-19T12:19:17 svc:/system/device/fc-fabric:default
online         2025-04-19T12:19:17 svc:/system/dump:config
online         2025-04-19T12:19:17 svc:/system/security/security-extensions:kernel
online         2025-04-19T12:19:18 svc:/network/npiv_config:default
online         2025-04-19T12:19:20 svc:/application/desktop-cache/icon-cache:default
online         2025-04-19T12:19:23 svc:/system/ca-certificates:default
online         2025-04-19T12:19:27 svc:/milestone/config:default
online         2025-04-19T12:19:27 svc:/network/install:default
online         2025-04-19T12:19:27 svc:/system/config-user:default
online         2025-04-19T12:19:27 svc:/system/keymap:default
online         2025-04-19T12:19:27 svc:/system/manifest-import:default
online         2025-04-19T12:19:28 svc:/network/ilomconfig-interconnect:default
online         2025-04-19T12:19:29 svc:/network/physical:default
online         2025-04-19T12:19:29 svc:/system/identity:cert-expiry
online         2025-04-19T12:19:30 svc:/network/iptun:default
online         2025-04-19T12:19:30 svc:/system/hal:default
online         2025-04-19T12:19:30 svc:/system/identity:domain
online         2025-04-19T12:19:30 svc:/system/identity:node
online         2025-04-19T12:19:30 svc:/system/identity:v12n-info
online         2025-04-19T12:19:30 svc:/system/identity:version
online         2025-04-19T12:19:31 svc:/application/desktop-cache/mime-types-cache:default
online         2025-04-19T12:19:31 svc:/milestone/network:default
online         2025-04-19T12:19:31 svc:/network/firewall/pflog-upgrade:default
online         2025-04-19T12:19:31 svc:/network/initial:default
online         2025-04-19T12:19:31 svc:/system/fcoe_initiator:default
online         2025-04-19T12:19:31 svc:/system/name-service/switch:default
online         2025-04-19T12:19:31 svc:/system/picl:default
online         2025-04-19T12:19:32 svc:/network/iscsi/initiator:default
online         2025-04-19T12:19:32 svc:/network/service:default
online         2025-04-19T12:19:32 svc:/network/ssh:default
online         2025-04-19T12:19:32 svc:/system/dump:deferred
online         2025-04-19T12:19:32 svc:/system/dump:disk
online         2025-04-19T12:19:33 svc:/system/swap:pass1
online         2025-04-19T12:19:34 svc:/milestone/devices:default
online         2025-04-19T12:19:34 svc:/milestone/single-user:default
online         2025-04-19T12:19:34 svc:/platform/efdaemon:default
online         2025-04-19T12:19:34 svc:/system/cluster/cl_analytics_init:default
online         2025-04-19T12:19:34 svc:/system/cluster/core-install:default
online         2025-04-19T12:19:34 svc:/system/cluster/geo-framework-uninstall:default
online         2025-04-19T12:19:34 svc:/system/cluster/svm-mediator-install:default
online         2025-04-19T12:19:34 svc:/system/cluster/telemetry-uninstall:default
online         2025-04-19T12:19:34 svc:/system/cluster/zones-uninstall:default
online         2025-04-19T12:19:34 svc:/system/device/hdlmload:default
online         2025-04-19T12:19:34 svc:/system/dump:act
online         2025-04-19T12:19:34 svc:/system/dump:swap
online         2025-04-19T12:19:34 svc:/system/swap:default
online         2025-04-19T12:19:35 svc:/milestone/name-services:default
online         2025-04-19T12:19:35 svc:/system/cluster/core-uninstall:default
online         2025-04-19T12:19:35 svc:/system/cluster/dsconfig-wizard-uninstall:default
online         2025-04-19T12:19:35 svc:/system/cluster/loaddid:default
online         2025-04-19T12:19:35 svc:/system/cluster/svm-mediator-uninstall:default
online         2025-04-19T12:19:35 svc:/system/cluster/telemetry-install:default
online         2025-04-19T12:19:35 svc:/system/cluster/zones-install:default
online         2025-04-19T12:19:35 svc:/system/filesystem/local:default
online         2025-04-19T12:19:35 svc:/system/name-service/cache:default
online         2025-04-19T12:19:35 svc:/system/swap:upgrade
online         2025-04-19T12:19:36 svc:/ldoms/ldmd_dir:default
online         2025-04-19T12:19:36 svc:/network/nfs/cleanup:default
online         2025-04-19T12:19:36 svc:/network/smb/client2:default
online         2025-04-19T12:19:36 svc:/system/auditset:default
online         2025-04-19T12:19:36 svc:/system/cron:default
online         2025-04-19T12:19:36 svc:/system/filesystem/ufs/quota:default
online         2025-04-19T12:19:36 svc:/system/restore_coreadm:default
online         2025-04-19T12:19:37 svc:/logstash:default
online         2025-04-19T12:19:37 svc:/system/boot-loader-update:default
online         2025-04-19T12:19:37 svc:/system/labeld:clearance
online         2025-04-19T12:19:38 svc:/network/routing-setup:default
online         2025-04-19T12:19:39 svc:/network/rpc/bind:default
online         2025-04-19T12:19:39 svc:/network/shares:default
online         2025-04-19T12:19:39 svc:/system/hdlm-initpath:default
online         2025-04-19T12:19:40 svc:/system/hdlm-manager:default
online         2025-04-19T12:19:40 svc:/system/hdlm-onlinepath:default
online         2025-04-19T12:19:44 svc:/application/security/compliance:generate-guide
online         2025-04-19T12:19:44 svc:/milestone/self-assembly-complete:default
online         2025-04-19T12:19:47 svc:/application/ds_agent:default
online         2025-04-19T12:19:47 svc:/system/fmd:default
online         2025-04-19T12:19:48 svc:/system/horcmR:default
online         2025-04-19T12:19:49 svc:/system/system-log:default
online         2025-04-19T12:19:52 svc:/system/devchassis:daemon
online         2025-04-19T12:19:53 svc:/ldoms/ldmd:default
online         2025-04-19T12:19:53 svc:/ldoms/vntsd:default
online         2025-04-19T12:19:53 svc:/system/kstat2adm:default
online         2025-04-19T12:19:54 svc:/network/smtp:sendmail
online         2025-04-19T12:19:54 svc:/system/auditd:default
online         2025-04-19T12:19:54 svc:/system/console-login:default
online         2025-04-19T12:19:54 svc:/system/filesystem/root-pool-config:default
online         2025-04-19T12:20:03 svc:/system/horcm:default
online         2025-04-19T12:20:08 svc:/system/install/telemetry-manager:files
online         2025-04-19T12:20:08 svc:/system/sstore:default
online         2025-04-19T12:20:09 svc:/system/cluster/osc-ha-zone-state-cleanup:default
online         2025-04-19T12:20:09 svc:/system/install/telemetry-manager:stats
online         2025-04-19T12:20:09 svc:/system/sysstat:default
online         2025-04-19T12:20:09 svc:/system/zones:default
online         2025-04-19T12:20:10 svc:/network/ntp:default
online         2025-04-19T12:20:20 svc:/milestone/multi-user:default
online         2025-04-19T12:20:23 svc:/system/auto-update:cleanup
online         2025-04-19T12:20:23 svc:/system/auto-update:stackdb
online         2025-04-19T12:20:23 svc:/system/coremon:cleanup
online         2025-04-19T12:20:24 svc:/system/boot-config:default
online         2025-04-19T12:20:24 svc:/system/coremon:default
online         2025-04-19T12:20:24 svc:/system/fm/asr-notify:default
online         2025-04-19T12:20:24 svc:/system/intrd:default
online         2025-04-19T12:20:24 svc:/system/polkit:default
online         2025-04-19T12:20:27 svc:/application/odoc-import:default
online         2025-04-19T12:20:57 svc:/milestone/multi-user-server:default
online         2025-04-19T12:20:58 svc:/application/texinfo-update:default
online         2025-04-19T12:20:58 svc:/milestone/goals:default
online         2025-04-19T12:20:58 svc:/system/zones-install:default
online         2025-04-19T12:21:49 svc:/application/man-index:default
online         2025-04-21T18:00:23 svc:/application/management/net-snmp:default
online         2025-05-22T21:02:22 svc:/system/sp/management:default
offline        2025-04-19T12:18:48 svc:/system/cluster/rgm-starter:default
offline        2025-04-19T12:18:53 svc:/system/cluster/gchb_resd:default
offline        2025-04-19T12:18:53 svc:/system/cluster/geo-framework-install:default
offline        2025-04-19T12:18:53 svc:/system/cluster/sc-rg-control:default
offline        2025-04-19T12:18:53 svc:/system/cluster/sc_ifconfig_server:default
offline        2025-04-19T12:18:53 svc:/system/cluster/sc_pnm_proxy_server:default
offline        2025-04-19T12:18:53 svc:/system/cluster/sc_rtreg_server:default
offline        2025-04-19T12:18:53 svc:/system/cluster/sc_syncsa_server:default
offline        2025-04-19T12:18:53 svc:/system/cluster/sckeysync:default
offline        2025-04-19T12:18:55 svc:/system/fm/smtp-notify:default
maintenance    2025-04-19T12:21:20 svc:/system/check/user:default
uninitialized  2025-04-19T12:18:44 svc:/application/cups/in-lpd:default
uninitialized  2025-04-19T12:18:47 svc:/network/rpc/meta:default
uninitialized  2025-04-19T12:18:48 svc:/network/rpc/rstat:default
uninitialized  2025-04-19T12:18:51 svc:/network/chargen:dgram
uninitialized  2025-04-19T12:18:51 svc:/network/chargen:stream
uninitialized  2025-04-19T12:18:51 svc:/network/daytime:dgram
uninitialized  2025-04-19T12:18:51 svc:/network/daytime:stream
uninitialized  2025-04-19T12:18:51 svc:/network/discard:dgram
uninitialized  2025-04-19T12:18:51 svc:/network/discard:stream
uninitialized  2025-04-19T12:18:51 svc:/network/echo:dgram
uninitialized  2025-04-19T12:18:51 svc:/network/echo:stream
uninitialized  2025-04-19T12:18:51 svc:/network/finger:default
uninitialized  2025-04-19T12:18:51 svc:/network/rpc/smserver:default
uninitialized  2025-04-19T12:18:52 svc:/network/kz-migr:stream
uninitialized  2025-04-19T12:18:52 svc:/network/login:rlogin
uninitialized  2025-04-19T12:18:52 svc:/network/nfs/rquota:default
uninitialized  2025-04-19T12:18:52 svc:/network/rexec:default
uninitialized  2025-04-19T12:18:52 svc:/network/rpc/mdcomm:default
uninitialized  2025-04-19T12:18:52 svc:/network/rpc/metacld:default
uninitialized  2025-04-19T12:18:52 svc:/network/rpc/metamed:default
uninitialized  2025-04-19T12:18:52 svc:/network/rpc/metamh:default
uninitialized  2025-04-19T12:18:52 svc:/network/rpc/rusers:default
uninitialized  2025-04-19T12:18:52 svc:/network/rpc/scadmd:default
uninitialized  2025-04-19T12:18:52 svc:/network/rpc/scrcmd:default
uninitialized  2025-04-19T12:18:52 svc:/network/rpc/scrinstd:default
uninitialized  2025-04-19T12:18:52 svc:/network/rpc/spray:default
uninitialized  2025-04-19T12:18:52 svc:/network/rpc/wall:default
uninitialized  2025-04-19T12:18:52 svc:/network/security/ktkt_warn:default
uninitialized  2025-04-19T12:18:52 svc:/network/shell:default
uninitialized  2025-04-19T12:18:52 svc:/network/talk:default
uninitialized  2025-04-19T12:18:52 svc:/network/telnet:default
uninitialized  2025-04-19T12:18:52 svc:/network/tftp/udp6:default
uninitialized  2025-04-19T12:18:52 svc:/network/time:dgram
uninitialized  2025-04-19T12:18:52 svc:/network/time:stream
incomplete     2025-04-19T12:18:56 svc:/system/ocm:default
