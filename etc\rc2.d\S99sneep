#!/bin/sh

# Copyright (c) 2010, 2015, Oracle and/or its affiliates. All rights reserved.
#
# $Id: sneep,v 1.5 2014/11/05 18:10:59 SNEEP Exp $

# Set and retrieve platform (chassis) serial number using system EEPROM.
# Can also be used to store and retrieve other things from EEPROM such as
# asset tags, contact information, and serial numbers for attached storage.
#
# Also attempts to use other sources for serial number including hardware-based
# mechanisms such as "prtdiag" and "smbios", configuration-based mechanisms
# such as the "explorer" and "CST" configurations, and (potentially) hybrid
# mechanisms such as serial data pulled from ILOM or System Controllers and
# stored in "explorer" output archives.
#
# Updates the serial number in the CST configuration file(s)
# if possible to simplify maintenance of these configuration items.
#
# Optionally allows the use of a simulation of the SMS command line interface
# on systems which do not have SMS or FRUID.
#
#   The "native" interface
#    sneep -s serialnumber      # Put serial number in EEPROM
#    sneep                      # Retrieve serial numnber
#    sneep -t tag -s setting    # Save entry in eeprom for "tag" with "value"
#    sneep -t tag               # Retrieve "value" from eeprom for "tag"
#
#  The SMS 1.4 interface for serial numbers
#    showplatform -p csn  # Print serial number
#    setcsn -c serial     # Put serial number in EEPROM
#
# Stores the system serial number in the nvramrc as a FORTH "print" command:
#       ." ChassisSerialNumber 1234ABCD " cr
# Note: This does not require OBP variable use-nvramrc? to be "true"
#       but if true, OBP will print the serial number while booting

# Save program's pathname and name
FullProg=$0
Prog=`basename $0`

# External version used in package, docs, usage
# Note: The makefile alters the release_version keyword below which is the
# release used in the package and docs. The Revision is the internal file
# revision from SCCS
version="Release 8.08-20150609"

#               %A% %G% %U%
#               <EMAIL>    2004/01
#
#	Use of FORTH "print" to store values:
#		<EMAIL>   <EMAIL>

# Null device
NULLFILE=/dev/null

# Enable tracing within functions
tracing=false

# Indicator that tells if an attempt was made to update eeprom
# It is the deciding factor for updating the current boot archive.
# Call bootadm update-archive if the indicator is true do not call bootadm
# update-archive if it is false.
eeupdated=false

# Check command-line options
case "$-" in
 *x*)
  # Check for tracing flag "x"
  tracing=true

  # Used when sneep is called from sneep
  traceflag="-x"

  # See stderr when tracing
  NULLFILE=/dev/tty
  ;;
esac

# Check sneep data sources consistency
sneep_serial_consistency()
{
 allsources=true
 verbose=true
 SSC_ALL_SERIALS=`get_multi "$DESIREDTAG" "$fieldsep"`
 SSC_SERIALS=`echo "$SSC_ALL_SERIALS" | grep -v "SerialNumber from" | sort -u`
 SSC_NSERIALS=`echo $SSC_SERIALS | wc -w`
 if [ "$SSC_NSERIALS" -eq 1 ]
 then
  if test "$SSC_SERIALS" = "unknown"
  then
   echo "Sneep reports that the serial number is unknown."
   echo "To set the serial number, use 'sneep -s ActualSerialNumber'"
   return 1
  else
   echo "All sneep data sources are consistent."
   echo "Serial Number: $SSC_SERIALS"
   return 0
  fi
 else
  REPORTDATASOURCES=`echo "$SSC_ALL_SERIALS" | \
   nawk '$2 == "" {printf "%-10s : %-25s\n", source ,$1; next } {source=$3}'`
  echo "Sneep has found more than one possible value for Serial Number."
  echo "The Serial Numbers available on this system are:"
  echo "$REPORTDATASOURCES"
  echo "To change the serial number reported, use 'sneep -s ActualSerialNumber'"
  return 1
 fi
}

# Emit error message on stderr
errmsg()
{
 echo "$@" 1>&2
}

# Support for testing and simulation. When tracing, we can see the data flow
# into the variable before it gets passed on.
show_dataflow()
{
 $tracing && set -x
 sd_dataflow=`cat`
 echo "$sd_dataflow"
}

# Simulation
: SIMULATION=$SIMULATION  SIMBASE=$BASE EXPLO_BASE=$EXPLO_BASE
SIMULATION=${SIMULATION:-false}
case "$SIMULATION" in
 true)
  # ZONESIM allows us to test code paths for non-global zones
  case "$ZONESIM" in
   "")
    # Null: not simulating a zone
    ZONEPATH=""
    ;;
   */)
    # Have a trailing "/"
    ;;
   *)
    # Add a trailing "/"
    ZONESIM="$ZONESIM"/
    ;;
  esac
  if test -z "$EXPLO_BASE"
  then
   # Simulation for this domain
   explorer_sim=false
   SIMBASE=${SIMBASE:-sim}	# use known simulation data
   ZONEPATH="$ZONESIM"	# use separate zone data
  else
   # Get simulation data from expanded exlorer at $EXPLO_BASE
   explorer_sim=true
   SIMBASE=$EXPLO_BASE

   # Use same explorer data for zone sim
   ZONEPATH=""
  fi

  # Some data goes in hierarchy for zone under base
  # e.g. sim/zonename/etc/default/SUNWsneep
  SIMBASE=$SIMBASE$ZONEPATH
  if test ! -d $SIMBASE
  then
   errmsg Cannot locate directory for simulation data \""$SIMBASE"\"
   exit 1
  fi

  # Note any simulated zone
  : ZONESIM=$ZONESIM
  : ; :  NOTE: SIMULATION ... zone=$ZONESIM
  export SIMULATION

  # Want to see things normally junked
  test -t 0 && NULLFILE=/dev/tty || NULLFILE=/dev/fd/2
  ;;
 false)
  # Use real data based at filesystem root
  SIMBASE=""
  explorer_sim=false
  ;;
 *)
  errmsg SIMULATION must be true or false
  exit 1
  ;;
esac

# Backup of sneep data in the filesystem
# owner:group and permissions on $BACKUP. Must be writable for owner.
BACKUP=$SIMBASE/etc/default/SUNWsneep
BACKUP_USER=root:other
BACKUP_PERMS=u=rw,go=r

# Be sure of what we are getting by putting system directories first
# especially for finding eeprom.
PATH=/sbin:/usr/bin:/usr/sbin:$PATH
export PATH

# Return hostid for the current host or simulation
get_hostid()
{
 if $SIMULATION
 then
  gh_rval=unknown_hostid
  expreadme=$SIMBASE/README
  if test -f $expreadme
  then
   gh_info=`awk ' /^Hostid:/ {print $2}' $expreadme `
   test ! -z "$gh_info" && gh_rval=$gh_info
  fi

  # Environmental override
  echo ${HOSTID:-$gh_rval}
 else
  hostid
 fi
}

# Return hostname for the current host or simulation
get_hostname()
{
 if $SIMULATION
 then
  gh_rval=unknown_hostname
  expreadme=$SIMBASE/README
  if test -f $expreadme
  then
   gh_info=`awk ' /^Hostname:/ {print $2}' $expreadme `
   test ! -z "$gh_info" && gh_rval=$gh_info
  fi

  # Environmental override
  echo ${HOSTNAME:-$gh_rval}
 else
  hostname
 fi
}

# Return uname option desired or simulation. Simulation is way too much work!
get_uname()
{		
 if $SIMULATION
 then
  : Simulation follows
 else
  uname "$@"
  return
 fi

 # Simulate uname and various options. Get much of it from README
 expfile=$SIMBASE/README
 if test -r "$expfile"
 then
  gu_platforms=`	# need uname -i
			# explorer contains drv/platform/uname -i
			# and maybe kernel/dev/uname -m
			kd=$SIMBASE/sysconfig/drv/platform
			test -d $kd && (cd $kd; echo *)
			# pass this to nawk later in variable
		`
  gu_all=`	
			# get full uname -a for passing to nawk in variable
			test -r $SIMBASE/sysconfig/uname-a.out &&
				cat $SIMBASE/sysconfig/uname-a.out
		`

  # Find uname values in README. Output as several lines of -<option> <value>
  gu_vals=` nawk -F:	-v platforms="$gu_platforms" \
					-v uname_a="$gu_all" \
			'
			/^Release/ { r = $2 ; print "-r " r}
			/Kernel architecture/ {m = $2; print "-m" m}
			/Application archite/ {p = $2; print "-p" p}
			/System Type/ { type = $2 ; if (type=="")type="unknown"}
			END {
				sub(m, "", platforms); # remove uname -m
				print "-i " platforms;
				print "-a " uname_a;
			}
			' $expfile
		`
 fi

 # Extract desired uname option from gu_vals
 echo "$gu_vals" | sed -n "s/^$1 //p"
}

# Important info about this machine can be overridden for testing or simulation
# by setting the appropriate environment variables
UNAME_I=${UNAME_I:-`get_uname -i`} # Platform name
UNAME_P=${UNAME_P:-`get_uname -p`} # ISA or processor type
UNAME_A=${UNAME_A:-`get_uname -a`} # Items including some model info
UNAME_M=${UNAME_M:-`get_uname -m`} # Hardware type (discouraged)

# Use $eeprom to find standard eeprom program
eeprom=eeprom
pkginfo=/usr/bin/pkginfo

# -- Simulation functions

if $SIMULATION
then

 # Use $eeprom to find eeprom simulation
 eeprom=eeprom_sim

 # Simulate eeprom
 eeprom_sim()
 {
	ee_var="$1"	# variable to get or set


	ee_dir=$SIMBASE/sysconfig/
	ee_stdfile=$SIMBASE/sysconfig/eeprom.out	# standard eeprom file

	ee_sims=$SIMBASE/sysconfig/eeprom	# dir for variable files

	case "$ee_var" in
	*=*)	# set eeprom variable.
		# extract value - minor issue is that it loses
		# 	trailing blanks and trailing blank lines
		# 	because of the backquoting
		ee_val=`echo "$ee_var" |
			sed -n -e \
   '1s/^[0-9abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ_\?\-]*=//p' \
				-e '2,$p' `
		# extract variable name
		ee_var=`echo "$ee_var" | sed -n '1s/=.*//p' `

		#echo _________ eeprom variable is "$ee_var" 1>&2
		#echo _________ eeprom value is \""$ee_val"\" 1>&2
		#echo "$ee_val"'\c' > eeprom.$ee_var  # no newline

		# we will store the value in a file just for this variable
		# make sure there is a directory to keep the variable/file in.
		test -d $ee_sims || mkdir -p $ee_sims

		# save the value into the file for this variable
		echo "$ee_val" > $ee_sims/$ee_var
		;;

	*)	# get eeprom variable
		ee_file=$ee_sims/$ee_var  # name of data file for this variable

		if test -s $ee_file ; then
			: have a file for this variable: return the value
			# returned string starts with <var>=
			echo "${ee_var}=\\c"	
			cat $ee_file
			echo ""		# output always ends with newline
		else
			: no file for $ee_var variable
			# look in standard eeprom.out file
			ee_file=$ee_stdfile
			if test ! -r $ee_file ; then
				# no eeprom.out, so all data is unavailable
				echo $ee_var: data not available.
				return 0
			fi

			# Get the value from the eeprom.out file
			# Allow for multiple lines of data.
			ee_val=` nawk 	-v eqpat="^$ee_var="  \
					-v nopat="^$ee_var: data" \
			'
				done == 1 { next }	# consume all data

				# look for variable with "no data" message
				$0 ~ nopat { print ; done = 1; next }

				# look for <variable>=<whatever>
				# 	print first line and look for more
				$0 ~ eqpat {
					print ; # print line of target variable
					printed = 1;
					next
				}

				# entry ends at next var with no data
				# or next <variable>=<value>
				printed && $0 ~ ": data not av" {
					done = 1 ; next
				}
				printed && $0 ~ "="	{ done = 1 ; next }
				
				# print another line of desired variable
				printed { print }
					
			' $ee_file `

			if test ! -z "$ee_val" ; then
				echo "$ee_val"
			else
				# didnt find line for variable in file
				echo $ee_var: data not available.
			fi
		fi
		return 0
		;;
	esac
 }

 # Simulate prtconf
 prtconf_sim()
 {
  pf_file=$SIMBASE/sysconfig/prtconf-vp.out
  test -f $pf_file && cat $pf_file
  return $?  # 0 if file, 1 if no file
 }

 # Simulate prtdiag
 prtdiag_sim()
 {
  pd_file=$SIMBASE/sysconfig/prtdiag-v.out
  pd_statusfile=$pd_file.status
  test -f $pd_file && cat $pd_file

  # Get exit status code from status file and return it
  # Status file overrides default status of $PRTDIAG_STATUS
  if test -s $pd_statusfile
  then
   # Ignore comments and ensure status code begins with a number
   pd_status=`sed -n -e 's/#.*//'  -e '/^[0-9]/p' $pd_statusfile`

   # Remove extra whitespace
   pd_status=`echo $pd_status`
   test ! -z "$pd_status" && return $pd_status
  fi

  # If there is a default status value for prtdiag, return it
  test ! -z "$PRTDIAG_STATUS" && return $PRTDIAG_STATUS

  # Otherwise always succeed
  return 0
 }

 # Simulate prtpicl
 prtpicl_sim()
 {
  pp_file=$SIMBASE/sysconfig/prtpicl-v.out
  test -f $pp_file && cat $pp_file
  return $?  # 0 if file, 1 if no file
 }

 # Simulate smbios
 smbios_sim()
 {
  sm_file=$SIMBASE/sysconfig/smbios.out
  test -f $sm_file && cat $sm_file
  return $?  # 0 if file, 1 if no file
 }

 # Simulate ipmi
 ipmi_sim()
 {
  IP_FILE="${SIMBASE}/ipmi/ipmitool_fru.out"
  test -f "${IP_FILE}"  && cat "${IP_FILE}"
  return $?  # 0 if file, 1 if no file
 }

 # Simulate pkginfo
 # Explorer-based simulation needs its own pkginfo
 pkginfo=pkginfo_sim
 pkginfo_sim()
 {
  if test $explorer_sim = false
  then
   : not from explorer files, so we can use real domain info
   /usr/bin/pkginfo "$@"
   return $?
  fi
  : simulate from explorer files
  ps_files=""

  # Main option to pkginfo
  ps_option=""

  # Package name, if any
  ps_pkg=""
  ps_quiet=false

  # Assume files for all pkgs
  ps_files="	$SIMBASE/patch+pkg/pkginfo-i.out
		$SIMBASE/patch+pkg/pkginfo-p.out "

  # See if there is an option and/or package
  case "$1" in
   -*)
    ps_option="$1" ; ps_pkg="$2"
    ;;
   *)
    ps_option="" ; ps_pkg="$1"
    ;;
  esac

  # Set pattern to match based on package name
  ps_pattern="$ps_pkg"

  # Any package matches if none specified
  test -z "$ps_pattern" && ps_pattern='.*'
  case "$ps_option" in
   -p)

    # Partial
    ps_files=$SIMBASE/patch+pkg/pkginfo-p.out
    ;;
   -i)

    # Full
    ps_files=$SIMBASE/patch+pkg/pkginfo-i.out
    ;;
   -q)

    # Status: is the package installed
    # Option -q needs package
    test -z "$ps_pkg" && return 1
    ps_quiet=true
    ;;
   -r)

    # Installation base for relocatable package
    ps_files=$SIMBASE/patch+pkg/pkginfo-l.out
    ps_hits=`nawk -v pat="^$ps_pattern\$" '
			/^[ 	]*$/	{ searching=0 ; next }
			$1 == "PKGINST:" && $2 ~ pat {
				searching = 1
				next
			}
			searching && $1 == "BASEDIR:" {
				print $2
				searching = 0
			}
			' $ps_files 2> $NULLFILE
		`  # end ps_hits=
    if test -z "$ps_hits"
    then
     echo ERROR: Information for \"$ps_pkg\" was not found 1>&2
     return 1
    fi
    echo "$ps_hits"
    return 0
    ;;
   *)
    echo ERROR: pkginfo_sim does not simulate "$ps_option" $ps_pkg
    return 1
    ;;
  esac
  : See if we can match some package info

  # First 2 lines of files are probably noise
  ps_hits=` nawk -v pat="^$ps_pattern\$" '
		FNR == 1 && /pkg/ { next } # noise
		FNR == 2 && /[=]/ { next } # noise
		$2 ~ pat {print}' $ps_files \
			2> $NULLFILE | sort -k 2,2
	` # end ps_hits=
  test -z "$ps_pkg" && ps_pkg="<any package>"
  if test ! -z "$ps_hits"
  then
   : Found something
   $ps_quiet || echo "$ps_hits"	# show hits
   return 0
  else
   : No matches found for pkginfo $ps_option
   case "$ps_option" in
    -p)
     if test ! -z "$ps_pkg"
     then
      echo ERROR: No partial information for \"$ps_pkg\" was found
      return 1
     fi
     return 0
     ;;
    -i | -q)
     $ps_quiet || echo ERROR: Information for \"$ps_pkg\" was not found 1>&2
     return 1
     ;;
   esac
  fi
  return 0
 }

 # Simulate sms showplatform
 sms_showplatform_sim()
 {
  test -r $SIMBASE/sf15k/showplatform.out &&
   cat $SIMBASE/sf15k/showplatform.out && return 0
  test -f $SIMBASE/sf15k/showplatform-p_csn.out &&
   cat $SIMBASE/sf15k/showplatform-p_csn.out

  # Return 0 if file, 1 if no file
  return $?
 }

 # Simulate sms platforminfo
 sms_platforminfo_sim()
 {
  # Need the SMS "PCD" platform_info file
  sps_info=$SIMBASE/sf15k/pcd/platform_info
  test -r $sps_info && cat $sps_info

  # Return 0 if file, 1 if no file
  return $?
 }

 # Simulate logger which will not really send the message to syslog
 logger()
 {
  errmsg logger "$@"
 }
fi

# -- End of Simulation functions

# Sneep uses 'tr' for several data format checks,
# but /usr/bin/tr does not work properly for UTF-8 locales.
# The XPG4 or XPG6 versions of tr are required for those locales.
# Set $tr to a suitable version if possible
for tr in /usr/xpg6/bin/tr /usr/xpg4/bin/tr /usr/bin/tr
do
 test -x $tr && break
done

# Warn of possible problems with tr and locale
case "$tr" in
 /usr/bin/tr)

  # Have to use default version : check for problems
  trout=`echo abc | $tr '[:lower:]' '[:upper:]' 2>&1 `
  if echo "$trout" | grep -i "bad string" > /dev/null
  then
   errmsg NOTE: $tr does not support your locale,
   errmsg "   " and no XPG4 alternative is available on this system.
   errmsg "   " Using locale C for sneep entries
   LC_ALL="C"
   export LC_ALL
   LANG=C
   export LANG
  fi
  ;;
esac

# Create a real tab character
tab=`echo @ | $tr @ '\t' `

# Get the current username.
# Hope that id in this locale returns something like uid=0(root) gid=1(other)
# and split on the parenthesis
thisuser=`id | ( IFS="()" ; read x name y ; echo $name) `

# Send the command line parameters to syslog() with user details
# Desirable for traceability of changes in audits. When invoked with -e option,
# logs the message to STDERR in addition of sending it to syslog().
log()
{
 $tracing && set -x
 case "$1" in
  -e)
   shift
   errmsg $Prog: "$@"
 esac

 # The message to log is passed on the command line
 l_message="$@"

 # Syslog facility level. This is not a daemon, but we need to be as sure as
 # possible that this log data is saved, and facility user.* is often ignored.
 # User can override in environment variable SNEEP_SYSLOG
 SYSLOG_FAC_LEVEL=${SNEEP_SYSLOG:-daemon.notice}

 # Figure out who is using the program if possible
 l_idprog=/usr/xpg4/bin/id
 if test -x $l_idprog
 then
  l_user=`$l_idprog -u -r -n`
 else
  l_user=$thisuser
 fi
	
 # Since this program is generally run by root (often via sudo) see if we can
 # find out if we were someone else before we became root
 # check SUDO_USER, USER, and LOGNAME
 l_moreuser=${SUDO_USER:-${USER:-$LOGNAME}}
 if test "$l_moreuser" = "$l_user"
 then
  l_userval="$l_user"
 else
  l_userval="$l_user:$l_moreuser"
 fi
 logger -p $SYSLOG_FAC_LEVEL $Prog:$l_userval "$l_message"
}

# Display usage
usage()
{
 # Pass "true" for full usage
 usage_full=${1:-false}
 cat 1>&2 << ENDUSAGE

	Save and retrieve Chassis Serial Number  ( CSN ) using EEPROM.

 usage:

   $Prog [-aAFhTvV] [-t tag] [-s setting ] [-P ds1:ds2...] \\
				[-d default] [-o separator]
   setcsn -c serialnumber
   showplatform -p csn

	-h		This help message
	For detailed information, consult the man page. Try
		man -m usr/lib/sneepProg

ENDUSAGE
}

# example of eeprom nvramrc output
#	|$ eeprom nvramrc
#	|nvramrc=devalias vx-rootdisk /sbus@3,0/SUNW,fas@3,8800000/sd@0,0:a
#	|devalias vx-rootmir /sbus@3,0/SUNW,fas@3,8800000/sd@1,0:a
#	|." ChassisSerialNumber FW02110044 " cr
#
# Note that if someone creates this entry without this script,
#	there may or may not be spaces before Chassis and after the serialnumber
#


# example of output format (from actual SC running SMS 1.4) :
#	|sc0:sms-user:> showplatform -p csn
#	|     CSN:
#	|     ====
#	|     Chassis Serial Number: 353A00053
#
# This is the de-facto API .
# for something simpler, use the "native" command usage . See the usage()

# Find eeprom, avoid other PATH pollution
PATH=/sbin:/usr/sbin:/usr/bin:$PATH
export PATH

# Solaris 10 has Zones. Non-global zones have read-only eeprom access.
# This causes some problems, so figure out if running in a non-global zone.
# Succeed if running in a non-global zone, fail on anything else
ngzone()
{
 $tracing && set -x

 # Allow for simulation of non-global zone : claim to be a zone
 $SIMULATION && test ! -z "$ZONESIM" && return 0

 # Use remembered return status $ngz_return after the first call
 # so that we dont have to call pkginfo repeatedly
 test ! -z "$ngz_return" && return $ngz_return

 # All zones should have the zonename command; fail if missing
 test -f /sbin/zonename || return 1

 # If the command is present, make sure that it is the real thing
 # and we have the package SUNWzoneu
 ngz_return=1
 $pkginfo -q SUNWzoneu || return 1   # fail if no zone package
 ngz_name=`/sbin/zonename`
 : zone name is \""$ngz_name"\"
 case "$ngz_name" in
  global)

   # Fail as we are on global zone
   return 1
   ;;
  "")

   # Something went wrong or fake command
   return 1
   ;;
  *)

  # Some other zone name : non-global !
  ngz_return=0
  ;;
 esac
 return $ngz_return
}

# Generate internal format for nvramrc
# Reformat nvramrc contents to multi-line format with simple formatting of
# sneep entries. This helps to deal with a Veritas VxVm 4.x change which puts
# everything on one line in the nvramrc. (Changed back in  VxVM Maintenance
# Patch 2 on sparc but not on Solaris x86). The FORTH in OBP seems to deal with
# this OK, but it is hard to read and make changes to, and seriously messes up
# customer nvramrc  programs. The multiline internal format makes it much
# easier to find, extract and generate sneep entries
nvramrc_internal()
{
 # We insert tab characters as needed where we want a newline,
 # and convert those tabs to newlines later with "tr" .

 # Remove "nvramc=" leader, empty warning,
 # Change sequences of whitespace into single space
 # Put devalias entries on separate lines
 # Put "cd" commands on separate lines
 # Put sneep entries in standard form:
 #  <startline><TaGwOrD>tag<VaLuE>value<EnDvAlUe><endofline>
 # Putting each sneep entry on its own line allows us
 # to match the maximum length strings using .*
 $eeprom nvramrc 2> $NULLFILE | show_dataflow |
  sed -e 's/^nvramrc=//' -e "/nvramrc:.*data not avail.*/d" \
  -e "s/[ $tab][ $tab]*/ /g" -e "s/ *devalias/${tab}devalias/g" \
  -e "s/\.\" */$tab<TaGwOrD>/g" -e "s/ *\" cr */<EnDvAlUe>$tab/g" \
  -e "s/\(<TaGwOrD>[^ ]*\) /\1<VaLuE>/g" -e "s/ cd /${tab}cd /g" \
  | tr '\t'  '\n' | sed -e "/^[ $tab]*\$/d"
}

# Put nvramrc data into external format (encoding sneep data in comments)
# and clean up noise and extras
nvramrc_external()
{

 # Convert internal format to external format
 show_dataflow | sed -e 's/^nvramrc=//' -e "/nvramrc:.*data not avail.*/d" \
  -e "s/[ $tab][ $tab]*/ /g" -e "/^[ $tab]*\$/d" -e 's/<TaGwOrD>/." /g'	\
  -e 's/<VaLuE>/ /g' -e 's/<EnDvAlUe>/ " cr/g' -e "s/[ $tab][ $tab]*/ /g" \
  | case $UNAME_P in
   i386 | *86*)	

    # Solaris x86 / x64 platform has simulated eeprom with only one line of
    # nvramrc. We will allow multiple things on one line by changing newlines
    # to spaces
    # Convert newlines to spaces
    tr -s '\n'    ' '

    # End with newline
    echo ""
    ;;
   *)

    # Presumably sparc. Although for the most part, the OBP FORTH seems to
    # handle everything all on one line, it is hard to read and maintain that
    # way. We prefer to leave entries on separate lines.
    cat -
    ;;
  esac
 return 0
}

# Retrieve tag or value number from eeprom - or return null if tag is missing
get_eeprom_value()
{
 $tracing && set -x

 # Optional tag to look for
 gev_tag=${1:-"$DESIREDTAG"}

 # Default is to get value otherwise get tags
 gev_getval=${2:-true}

 # Only 1 line returned, unless false
 gev_limit1=${3:-true}		

 # If true, use real eeprom only
 gev_eeonly=${4:-false}

 # Non-Global zones cannot write the eeprom, but we allow them to use the
 # backup file to override the global eeprom or to save zone-specific data.
 # As a result, when asked for eeprom data, we have to consult the backup file
 # first, unless the "eeonly" parameter says to use the real eeprom only.
 # (this is used when called from the backup file manager)
 if ngzone && test $gev_eeonly = false
 then

  # We have to look in the backup file first, and if we do not find the
  # desired data, check the real eeprom.
  : zone : look in backup file first
  if $gev_getval
  then
   backup get "$gev_tag" && return 0
  else
   backup gettags "$gev_tag" && return 0
  fi

  # Data not found in backup - check real eeprom
 fi

 # Real eeprom - whatever the "eeprom" command says is real, although actually
 # simulated on x86. Extract VALUE (or TAG) from <." TAG VALUE " cr>
 # Keep the last one if there are more than one (manual entry mistake?)
 # We try to allow for some flexibility for whitespace in  manually-entered tags.
 if $gev_getval
 then

  # Value is the substring between <VaLuE> and <EnDvAlUe> in "internal form"
  gev_tagmatch='<TaGwOrD>'"$gev_tag"'<VaLuE>\(.*\)<EnDvAlUe>'
 else

  # Tag is the substring between <TaGwOrD> and <VaLuE> in "internal form"
  gev_tagmatch='<TaGwOrD>\('"$gev_tag"'\)<VaLuE>.*'
 fi
 if $gev_limit1
 then

  # Limit to final entry if duplicates
  gev_limiter="sed -n '\$p' "
 else

  # No limit on number of entries
  gev_limiter="cat"
 fi
 gev_val=`nvramrc_internal | show_dataflow | sed -n -e "s/$gev_tagmatch/\1/p" \
  | eval $gev_limiter`

 # No such tag in eeprom
 test -z "$gev_val"  && return 1

 # Output value from eeprom
 echo "$gev_val"
 return 0
}

# Update eeprom with tag and value embedded in nvramrc
set_eeprom_value()
{
	$tracing && set -x

	se_value="$1"		# value to set
	se_tag=${2:-$DESIREDTAG}  # eeprom tag with which to associate value
	se_inform=${3:-true}	# if "quiet", prevents informational messages
				# (but still outputs critical errors)
	test "$se_inform" = quiet && se_inform=false  # translate to boolean

	# make sure the new value can be used
	#	should not contain control characters (including tabs)
	#	cannot contain double quotes
	#	  convert others to double quotes, look for double quotes
	#	  (need '\c' to prevent newline, which would become quote)
	#	   '["*0]' means "a string of quotes of the proper length"

	se_tst=`echo "$se_value"'\c' | $tr '[:cntrl:]'   '["*0]'  `
	if echo "$se_tst" | grep '"' > /dev/null ; then
		errmsg The value for nvramrc cannot contain double quotes \
			or control characters .
		return 1
	fi

	# before we set it, get the current value for this tag
	# making sure that we get only true eeprom data
	se_oldval=`get_eeprom_value "$se_tag" true true true`
	: set eeprom to \"$se_val\" , was \"$se_oldval\"

	# If the new value is null, the value is to be removed.
	# If the old value is also null, (absent) this could indicate
	#	a mistake (incorrect tag)
	if test -z "$se_value" -a -z "$se_oldval" ; then
		$se_inform && errmsg \""$se_tag"\" was not present in nvram .

		: no need to go on with setting the eeprom:
		# might even eliminate potential problems
		return 0	# success: tag has been "removed"
	fi

	# non-global zone cannot write eeprom:
	# pretend that setting it succeeds,
	# both to reduce unhelpful error messages,
	# and to permit other changes to proceed (e.g. explorer and CST)
	#	Note that to properly virtualize the eeprom  for zones
	#	using the backup file, we should call "backup set" here,
	#	but backup set is always called anyway if set_eeprom succeeds
	ngzone && : pretend that ngzone eeprom was set && return 0

	# Real eeprom update
	# save full nvram contents in case of changes to format
	se_oldintern=`nvramrc_internal`  # save in internal form
		# save cleaned-up external form too
	se_oldextern=`$eeprom nvramrc 2> $NULLFILE | nvramrc_external `

	# sneep entries in internal form:
	#  <startline><TaGwOrD>tag<VaLuE>value<EnDvAlUe><endofline>

	# remove the entry if it has changed, and
	# add it back in later (if necessary).
	# 	This allows us to delete entries
	#	by giving a null value.
	#
	#	Only remove if changed so that we
	#	can tell the difference between
	#	content changes and format changes,
	#	and optimize them.
	se_oldnvram="$se_oldintern"
	if test "$se_value" != "$se_oldval" ; then
		# remove old entry from nvramrc (if present)
		se_oldnvram=`echo "$se_oldintern" |
			sed	-e "/^<TaGwOrD>$se_tag<VaLuE>/d" `
	fi

	# start building the new nvramrc contents
	# from the remaining old contents
	se_newnvram="$se_oldnvram"

	# Only add the value to nvramrc if new value is non-null
	# and if the value changed.
	if test ! -z "$se_value" -a "$se_value" != "$se_oldval" ; then
		# The new, changed value is not null. replace it

		if test  -z "$se_oldnvram" ; then
			# nvramrc was empty; put tag/value in as only entry

			# instead of simple assignment, use same backquote
			# method used for non-empty case for consistency
			#    se_newnvram=".\" $se_tag $se_value \" cr"
			se_newnvram=`
			   echo "<TaGwOrD>$se_tag<VaLuE>$se_value<EnDvAlUe>"
			`
		else
			# nvramrc was not empty ;
			# add new serial entry to what was already there
			se_newnvram=`	
			   echo "$se_oldnvram"
			   echo "<TaGwOrD>$se_tag<VaLuE>$se_value<EnDvAlUe>"
			`
		fi
	fi

	# put nvramrc data into external format
	se_newnvram=`echo "$se_newnvram" | nvramrc_external `

	# If the old and new data is the same, dont bother.
	#
	# 	We prefer to check the full eeprom contents
	# 	rather than just the value for this tag,
	# 	because conversion between internal and external
	#	formats may have had other desirable side effects
	#	like putting devalias entries on separate lines.
	if test "$se_newnvram" = "$se_oldextern" ; then
		$se_inform && errmsg \""$se_tag"\" no change to eeprom.

		# no need to go on with setting the eeprom:
		# might even eliminate potential problems
		return 0	# no change, no failure
	fi

	# ready to update the eeprom:
	# perform safety checks
	if check_nvramrc_limits "$se_newnvram" ; then
		: reasonable entry for this eeprom
	else
		errmsg New entry exceeds eeprom nvramrc limitations .
		return 1
	fi

	# we are attempting a change in eeprom.
	# lets mark this attempt,immaterial of its success.
	# we will later refer to this indicator to decide on updating
	# the current boot archive.
	eeupdated=true
	#
	# update the eeprom
	#
	if $eeprom nvramrc="$se_newnvram" 2> $NULLFILE
	then
		if test "$se_value" != "$se_oldval" ; then
			log Changed $se_tag in EEPROM nvramrc \
			from \"$se_oldval\" to \"$se_value\"
		else
			log Changed EEPROM nvramrc format \
			  without changing $se_tag value
		fi
			
		#
		# seems to have been successful: double-check result
		#
		se_newval=`get_eeprom_value "$se_tag" true true true `
		: v=\""$se_value"\"  nv=\""$se_newval"\"
		if test "$se_value" != "$se_newval" ; then
			return 1	# old and new value dont match
		fi			# fail
	else
		return 1	# failed to set nvramrc
	fi
	return 0		# successfully updated nvramrc
}

check_nvramrc_limits()
{
	$tracing && set -x
	cnl_newentry="$1"
	cnl_rval=0		# return value: assume success
	cnl_checkoverride=false	# check safety override true

	# set default value for maximum size of nvram contents
	cnl_maxsize=500		# by default, this is the most nvramrc we allow.
				# Slightly more than 512 is available on Sparc2.
				# Newer platforms generally provide more.
				# overflow causes a drop into OBP,
				# or corruption of other EEPROM variables,
				# and extreme cases can cause failure to boot

	# find out how big the given entry will be
	cnl_nsize=`echo "$cnl_newentry" | wc -c`

	# check for hardware platform limitations
	# uname -i returns something like "SUNW,Ultra-250" or "i86pc"
	case $UNAME_I in
	*)	# assume defaults are OK for any platform which we
		# do not specifically know of another restriction
		;;
	esac

	# check for limitations of current host's ISA or processor type
	# uname -p returns something like "sparc" or "i386"
	case $UNAME_P in
	i386 | *386* | *x86* )
		# x86 platform has multiple restrictions (and no true eeprom)
		# The system won't boot if the emulated eeprom is too large, so
		cnl_maxsize=500		# this has tested OK on solaris10

		# Solaris 10 x86 at FCS could only accept one line for nvramrc .
		#  we assume that prior versions were just as restrictive
		cnl_nlines=`echo "$cnl_newentry" | wc -l`
		if test $cnl_nlines -gt 1 ; then
			errmsg This platform is limited to one \
				entry in EEPROM nvramrc.
			errmsg To store this entry, remove any others first .
			cnl_rval=1
		fi
		;;
	sparc)
		# Need more details on the hardware for sparc.
		# "uname -m", "arch", and "mach" are all discouraged,
		# but uname is preferred.
		case $UNAME_M in
		sun4 | sun4c | sun4d )
			cnl_maxsize=500 ;;	# just over 500 available
		sun4u | sun4m )	
			cnl_maxsize=4000 ;;	# somewhat over 5000 available
		esac

		# V1280 and E2900 both use an EEPROM which is rather small
		#  system type is on first line, consume the rest
		case `prtdiagf -v |  sed -n 1p ` in
			*1280 | *2900 )		cnl_maxsize=275 ;;
		esac
		case $UNAME_A in
			*SUNW,Netra-T12 )	cnl_maxsize=275 ;; # V1280/E2900
		esac
		;;

	*)	cnl_isa=$UNAME_P
		errmsg $cnl_isa : Unknown platform ISA or \
					processor type "(from uname -p)" .
		cnl_errmsg=`echo Cannot know eeprom limitations from \
			uname -p of $cnl_isa .`

		cnl_checkoverride=true	# check for safety override
		cnl_rval=1		# otherwise fail
		;;
	esac

	# make sure that the entry will safely fit in nvram

	# As the current size could already be more than the default,
	# we will allow as much as we already have.
	cnl_cursize=`$eeprom nvramrc 2> $NULLFILE |
				sed 's/nvramrc=//' | wc -c`
	test $cnl_cursize -gt $cnl_maxsize &&
				cnl_maxsize=$cnl_cursize

	# make sure it doesnt exceed the maximum,
	#	unless overridden by user
	if test $cnl_nsize -gt $cnl_maxsize ; then
		# new value is larger than the supposed maximum.
		errmsg EEPROM nvramrc may not be large enough .
		cnl_errmsg=`echo Overriding nvramrc size limit of \
			$cnl_maxsize to save $cnl_nsize characters .`

		cnl_checkoverride=true	# check for safety override
		cnl_rval=1		# otherwise fail
	fi	
	if $cnl_checkoverride ; then
		# allow user to override, but log it if they do.
		if $override ; then
			log -e Safety override option has been used:
			log -e "$cnl_errmsg"
			sleep 1	# allow syslog to process the messages
			sync	# save any outstanding IO in case of crash
			sleep 5	# let the IO complete

			cnl_rval=0	# override chosen: must be OK
		fi
	fi

	return $cnl_rval
}

# Restart psncollector each time you set serial number to make PSN data
# consistent with Sneep. In solaris 10 and above restart psn service by using
# svcadm command, while in lower solaris version run the psncollector command
restart_psn()
{
 OS_VER=`uname -r | nawk -F'.' '{print $2}'`

 # Path to psncollector in solaris 8 and 9
 PSNPATH="/usr/lib/inet/psncollector"
 if pkginfo -q SUNWpsn
 then
  if [ "$OS_VER" -ge 10 ]
  then
   svcadm restart psncollector
  else
   if [ ! -x "$PSNPATH" ]
   then
    PSN_BASEDIR=`pkgparam SUNWpsn BASEDIR`
   fi
   if [ -x $PSN_BASEDIR/$PSNPATH ]
   then

    # PSN 1.1.2 and higher gives following output on success:
    #  Using /var/run
    #  Storing "serial" to /var/run/psn
    # While PSN 1.1.3 and higher outputs an emptyline on success.
    # Ignore these success messages
    $PSN_BASEDIR/$PSNPATH 2>&1 | egrep -v 'Using|Storing|^$'
   fi
  fi
 fi
}

# Print serial entry from all explorer config files
get_explorerconfig_serials()
{
 myid=`get_hostid`
 SERIALS=""

 # Look for EXP_SERIAL_<hostid>="<serial>"
 (IFS='|'; for expfile in ${explorer_configs}
 do
  test -r "$expfile" || continue
  SER=`sed -n -e 's/"//g' -e "s/^EXP_SERIAL_${myid}=//p" "$expfile"`
  test -n "${SER}" || continue
  if [ -z "${SERIALS}" ]
  then
   SERIALS="${expfile}=${SER}"
  else
   SERIALS="${SERIALS}|${expfile}=${SER}"
  fi
 done
 echo "${SERIALS}")
}

# Sneep no longer attempts to update the serial in any Explorer config file.
set_explorerconfig_serial()
{
 return 0
}

##########################################################
#
# Backup file interface
#
backup()	# save/retrieve tag and value from backup file
{		# if calling recursively, do it in subshell
		# to keep from reassigning "local" variables

	$tracing && set -x

	: backup "<$1> <$2> <$3>"
	bup_action="$1"	# action is first parameter

	bup_tag="$2"		# this arg is notmally the tag
	bup_option1="$2"	# but some actions may have option passed here
				# note that is is SAME arg as tag

	bup_val="$3"		# value for tag
	bup_option2="$3"	# possible additional argument
				# SAME arg as bup_val

	# backup file format has (potentially) multi-line entries
	# each of the form :
	#	<tag><tab><value>
	#
	# Note that may be more flexible than the eeprom,
	# so the backup file may accept things which cannot be
	# put in the eeprom.  ( Avoid this )
	#
	# Note that because of this
	#   ****the eeprom and the backup may not match****
	#
	# but the backup will always have the "best" information.
	# Getting it from backup and putting back in eeprom will
	# put the best possible thing in eeprom that it can handle.
	case "$bup_action" in
	"")	errmsg Internal error: no directions for backup .
		return 1
		;;
	setup)	
		#
		# Set up the backup file: take care with owner and perms
		#
		if touch $BACKUP 2> $NULLFILE ; then
			: $BACKUP is writable
		else
			: Cannot update $BACKUP as $thisuser .
			return 1
		fi

		# set permissions on existing file before
		# we try to write it.
		test -f $BACKUP && (
			chown $BACKUP_USER $BACKUP
			chmod $BACKUP_PERMS $BACKUP
		) 2> $NULLFILE
		return 0
		;;
	init)	# set up initial contents of backup file
		if ( backup setup ) && cp /dev/null $BACKUP 2> $NULLFILE ; then

			# put the hostid of this host in the backup file
			# as check for valid file
			echo "sneep hostid$tab`get_hostid`" > $BACKUP

			log $BACKUP successfully '(re)'initialized
			return 0
		else
			log -e $BACKUP : unable to initialize as user $thisuser
			return 1
		fi
		;;
	validate)	# make sure that $BACKUP is for this system
			# NOTE: includes code from "get" below

		# if we know it is OK, save a lot of work
		test "$backup_status" = "backup_valid" && return 0

		# validate has optional "fix" argument (true/false)
		# 	true means attempt to fix any problem
		#		by reinitializing backup file
		#	false means return 1 quietly if problem found

		bup_fix=${bup_option1:-true}	# fix problems by default
		if test ! -r $BACKUP ; then
			: missing or unreadable backup file
			# return 1 if no fix wanted, OR fix unsuccessful
			$bup_fix || return 1

			( backup init ) || return 1

			backup_status=backup_valid
			return 0	# backup now valid, even if empty
		fi

		# Backup file exists...
		# Retrieve value for hostid tag from the backup file
		#

		# return the value by stripping the tag and tab
		# from the relevant lines of the backup file
		bup_val=` sed -n "s/^sneep hostid$tab//p" $BACKUP `
		if test -z "$bup_val" ; then
			: no hostid in backup
			$bup_fix || return 1
			log $BACKUP is incomplete
			bup_copy=$BACKUP.incomplete
			if cp $BACKUP $bup_copy 2> $NULLFILE ; then
				log saved $BACKUP as $bup_copy
			else
				log -e unable to copy $BACKUP to $bup_copy \
					as user $thisuser
			fi
			(backup init) || return 1
			backup_status=backup_valid
			return 0	# backup is OK now
		fi

		# got some hostid - is it the right one?
		if test "$bup_val" != `get_hostid` ; then
			$bup_fix || return 1
			log $BACKUP is from a system with ID \"$bup_val\"
			# save original backup (from another system)
			# in case we have to sort it out later
			bup_copy=$BACKUP.$bup_val
			if cp -p $BACKUP $bup_copy ; then
				log saved $BACKUP as $bup_copy
			else
				log -e unable to save $BACKUP as $bup_copy\
					as user $thisuser
			fi
			(backup init) || return 1
			backup_status=backup_valid
			return 0	# backup is OK now
		fi
		: existing backup file seems to be valid
		backup_status=backup_valid
		return 0
		;;
	invalidate )	# mark backup file invalid in this shell
			# to avoid repeated log messages as we try to
			# get various sneep parameters from backup file

		backup_status=backup_invalid

			# have to do it this way because the problem is
			# usually discovered in a subshell and setting
			# the status variable when it is discovered
			# has no effect when we return to the caller

		return 0
		;;
	get)
		# silently fail if we know the backup cant be used
		test "$backup_status" = "backup_invalid" &&  return 1

		# check the validity , be willing to hear about it and fix it
		if ( backup validate ) ; then
			backup_status=backup_valid
		else
			backup_status=backup_invalid	# avoid repeated checks
			return 1
		fi

		#
		# Retrieve value for tag from the backup file
		#

		# return the value by stripping the tag and tab
		# from the relevant lines of the backup file

		bup_val=` sed -n "s/^$bup_tag$tab//p" $BACKUP `

		test -z "$bup_val" && return 1		# fail if no value

		echo "$bup_val"		# return value
		return 0		# return success
		;;

	gettags)
		# silently fail if we know the backup cant be used
		test "$backup_status" = "backup_invalid" &&  return 1

		# check the validity , be willing to hear about it and fix it
		if ( backup validate ) ; then
			backup_status=backup_valid
		else
			backup_status=backup_invalid	# avoid repeated checks
			return 1
		fi

	
		# return the tags by stripping the tab and value from
		# the relevant lines.
		#	do NOT return tags like "sneep hostid"
		sed -n -e '/^sneep /d' -e "s/$tab.*//p" $BACKUP	# return value
		return 0
		;;

	set)
		if ( backup setup ) ; then
			: should be able to update backup file
		else
			errmsg Cannot update $BACKUP as $thisuser .
			return 1
		fi

		# check for and repair any problems
		#	knowing from having already done successful setup
		#	that we can write the file
		if ( backup validate ) ; then
			backup_status=backup_valid
		else
			backup_status=backup_invalid	# avoid repeated checks
			return 1
		fi

		bup_oldval=`(backup get "$bup_tag" )`

		# null values we always want to set (to erase the entry)
		# but non-null values we have to think about

		# think about whether to set value in non-global zone
		if test ! -z "$bup_val" && ngzone ; then
			# Non-global zones have readonly nvram access
			# and we allow them to override the system eeprom
			# by use of the backup file.
			#
			# However, we want to use the eeprom as much as
			# possible, (particularly for Chassis Serial)
			# so that most things only have to be saved
			# in the global zone  once to be seen in all zones.
			#
			# We only override when absolutely necessary
			# so that there are fewer problems with redundancy.
			# (The global zone's backup file serves as
			#  full backup for the eeprom)
			#
			# To facilitate this, we do not put anything
			# in a zone's backup file which is
			# the same as the data in eeprom.

			# In typical and rational use, this is fine.
			# Still, it is possible for a zone to
			# find that something has changed unexpectedly
			# because it changed in the global zone after
			# we chose NOT to save it in the local zone backup file.
		
			# get value from eeprom and check for value match
			# so that we dont have to save it redundantly.
			# final "true" param makes sure that get_eeprom_value
			# uses the real eeprom  only

			bup_eeval=`get_eeprom_value "$bup_tag" \
							true true true  `
			if test ! -z "$bup_eeval" -a "$bup_eeval" = "$bup_val"
			then
				: no need to save $bup_tag data in eeprom
				: because it is already in eeprom
				: so we shall remove it from backup
				bup_val=""	# null value removes
			fi
		fi
		
		# dont update backup file if data has not changed
		test "$bup_val" = "$bup_oldval" && return 0

		#
		# Update the backup file
		#

		# get all lines except those for $bup_tag and hostid
		# this deletes the old entry for $bup_tag, if any
		bup_other=`sed	-e '/^sneep hostid/d' \
				-e "/^$bup_tag$tab/d" $BACKUP`
		
		# empty the backup file
		cp /dev/null $BACKUP 2> $NULLFILE || return 1

		# put the hostid of this host in the backup file
		# for boot-time autoload check
		echo "sneep hostid$tab`get_hostid`" > $BACKUP

		# if there is a new value, put it in the backup file
		# as the first entry.
		# Each line of the value starts with <tag><tab> .
		test ! -z "$bup_val" &&
			echo "$bup_val" | sed "s/^/$bup_tag$tab/" >> $BACKUP

		# put any other stuff back into the backup file
		test ! -z "$bup_other" && echo "$bup_other" >> $BACKUP

		return 0	# update succeeded
		;;
	esac
}

# Used to retrieve CSN from FUJITSU PRIMEPOWER devices, if available
# CSN in FUJITSU PRIMEPOWER machine is generally the output of
# command /opt/FJSVmadm/sbin/serialid
fserial_info()
{
 fs_action="$1"                  # get/set action
 fs_tag="${2:-$SERIAL_TAG}"      # optional tag
 fs_val="$3"     # value for tag  # optional value
 fs_name="fserial_info"		# name of the function
 FSERIAL_FILE="/opt/FJSVmadm/sbin/serialid" # Default path to run serialid command
 case "$fs_action" in
  "")
   errmsg Internal error: No action defined for $fs_name
   return 1
   ;;
  set)
   # Cannot set value - read-only, so return update succeeded
   return 0
   ;;
  get)
   # Retrieve value for fserial from the serialid command only
   # SERIAL_TAG is possible
   test "$fs_tag" = $SERIAL_TAG || return 1

   # The default path for serialid command is /opt/FJSVmadm/sbin/serialid
   # We will also look into alternate path if not found in the default path
   if [ ! -x $FSERIAL_FILE ]
   then
    FSERIAL_BASEDIR=`pkgparam FJSVmadm BASEDIR 2>$NULLFILE`
    test -z "$FSERIAL_BASEDIR" || return 1
    FSERIAL_FILE="$FSERIAL_BASEDIR/FJSVmadm/sbin/serialid"
   fi
		
    # Sample output of the serialid command
    # /opt/FJSVmadm/sbin/serialid
    # serialid: 010061		
    # Check again to be sure in case of alternate path
    if [ -x $FSERIAL_FILE ]
    then
     if [ "$thisuser" = "root" ]
     then
      # Store the output of fserialid command
      fserial_out=`$FSERIAL_FILE 2>$NULLFILE`
      if [ $? -ne 0 -o -z "$fserial_out" ]
      then 	
       log -e Unable to fetch serial number from fserial data source
       return 1
      fi

      # Make sure we only take serial number from the line containing serialid
      get_fserial=`echo "$fserial_out" | nawk '/serialid/  {print $2}'` 	
      if [ -n "$get_fserial" ]
      then
       echo $get_fserial
       return 0	# success
      else
       log -e serial number missing from output of $FSERIAL_FILE
      fi
     else
      errmsg Please run $Prog as root to ensure correct serial number \
       from fserial data source
      return 1
     fi
    fi

    # Serialid command not present
    return 1
    ;;
  *)
   errmsg Invalid action \""$fs_action"\" in $fs_name
   return 1
 esac
 return 1	
}

# Used to retrieve CSN from prtpicl
prtpicl_info()
{
 if [ "${UNAME_M}" = "sun4u" ]
 then
  if $SIMULATION
  then
   prtpicl=prtpicl_sim
  else
   for prtpicl in /usr/sbin/prtpicl /usr/platform/"$UNAME_I"/sbin/prtpicl
   do
    test -x "$prtpicl" && break
   done
   test -x "$prtpicl" || return 0
  fi
  picl_val=`"$prtpicl" -v 2> $NULLFILE | show_dataflow | grep ':chassis-sn' |
            sed -e 's/^.*:chassis-sn//g' -e "s/[ $tab][ $tab]*//g"`
  test -z "$picl_val" && return 1
  echo "$picl_val"
 fi
 return 0
}

##########################################################
#
# SMS (System Management Services) interface for chassis serial data
# Works for SF12000 / SF15000  System controllers
# Software-assisted "hardware" support initially set by installer using SMS
# setcsn -c <serialnumber>
# CSN from SMS if possible
sms_info()
{
 # Get/set action
 sms_action="$1"

 # Optional tag
 sms_tag="${2:-$SERIAL_TAG}"

 # Value for optional tag
 sms_val="$3"
 case "$sms_action" in
  "")
   errmsg Internal error: No action for SMS.
   return 1
   ;;
  set)
   # Cannot set value in SMS - read-only
   # We could pass this change along to SMS and setcsn, but we treat CSN as if
   # it was true hardware because it should only be set once at installation
   # time. If it gets done again, it should be done deliberately using SMS
   # procedures. Return update succeeded
   return 0
   ;;
  get)
   # Retrieve value for tag from the SMS. Only SERIAL_TAG is possible
   test "$sms_tag" = $SERIAL_TAG || return 1
   sms_showplatform_serial
   return $?
   ;;
 esac
 return 0
}

# Collect Chassis serial from SMS showplatform
sms_showplatform_serial()
{
 #root@SC0 # su sms-svc -c "/opt/SUNWSMS/bin/showplatform -p csn"
 #
 #CSN:
 #====
 #Chassis Serial Number: 226A20B7
 sms_csn=`

  if $SIMULATION
  then
   # Simulate showplatform output
   sms_showplatform_sim
  else
   sms_user=$thisuser

   # We need the SMS executable
   sms_exec=/opt/SUNWSMS/bin/showplatform
   test -x $sms_exec || sms_user=nobody

   # We need the sms-svc user on the system
   getent passwd sms-svc > $NULLFILE 2>&1 || sms_user=nobody

   # We need to know who we are
   case $sms_user in
    nobody )
     # Signal that showplatform will not work, output nothing
     ;;
    root )
     # Run showplatform as sms-svc user
     su sms-svc -c "$sms_exec -p csn" 2> $NULLFILE
     ;;
    sms-* )
     # Run showplatform as current SMS user
     $sms_exec -p csn 2> $NULLFILE
     ;;
   esac
  fi | show_dataflow | nawk '/^Chassis Serial Number/  {print $NF} {next}' -

        ` # end of code to gather $sms_csn
 if test -n "$sms_csn"
 then
  echo "$sms_csn"
  return 0
 fi

 ##### Gather SMS CSN from platform_info file
 sms_csn=`

  if $SIMULATION
  then
   # Simulate platform_info data
   sms_platforminfo_sim
  else
   sms_user=$thisuser

   # We need the SMS "PCD" platform_info file
   sms_info=/var/opt/SUNWSMS/SMS/.pcd/platform_info

   # Data format usable with this code same format from SMS 1.4 - 1.6
   # "!" denotes beginning of line
   # note empty field at beginning of data line
   # !# version 3 field count 14
   # !
   # ! |<PlatName>|<dig>|<dig>|<dig>|1|<IP>| | |<IP>|<IP>|<mask>|0|<SERIAL>

   # Permissions for the file:
   #-rw-------   1 <USER>  <GROUP>   170 Feb 26 19:09 platform_info

   # If we cannot find file, use username as an error signal
   test -f $sms_info || sms_user=nobody

   # We need to know who we are
   case $sms_user in
    nobody)
     # Signal that showplatform will not work, output nothing
     ;;
    root | sms-pcd)
     # Run showplatform as sms-pcd and current SMS user
     cat $sms_info 2> $NULLFILE
     ;;
    *)
     # Error, with explanation
     errmsg SMS : Unable to gather platform serial as user $thisuser
     errmsg SMS : Use \"sms-pcd\" or \"root\" for platform_info
     ;;
   esac
  fi | show_dataflow | nawk -F'|'  '
		/version [124-9]/ {exit} # Only does version 3 file format
		NF > 1 { print $NF }	 # Print final data field
	' -

        ` # end of code to gather $sms_csn from platform_info
 if test -n "$sms_csn"
 then
  echo "$sms_csn"
  return 0
 fi

 # No serial number. Fail
 return 1
}

##########################################################
#
# prtdiag interface for chassis serial data
#
# prtdiag on Solaris 10 and certain platforms has been
# observed to be quite slow.
# As we might need the data more than once, cache it.
# prtdiag may also  have to wait for data from picld
# so do the waiting here.

# We have found that because the calls to this function
# are made from various subshells, using a variable for
# the cache is ineffective.
# We are forced to use a file for the cache.

# We should create the temporary cache file in user writable space
# with an unpredictable file name so that even non-root user
# can also create and use it temporarily
# As 'mkdir' is atomic, we attempt to create a directory and the
# cache file inside it, if and only if the directory does not exist
# In case, the directory exists, sneep will not attempt to create cache
# Instead, sneep will probe prtdiag interface directly for data
mkdir -m 0700 /tmp/sneep_prtd.$$ > $NULLFILE 2>&1
test "$?" -eq 0 && prtdiag_cache=/tmp/sneep_prtd.$$/sneep_prtd.$$

# Just before termination of program, we should clean up temporary directory
# and file and exit
cleanup()
{
 # Ignore further signals
 trap "" 0 1 2 15
 /bin/rm -rf /tmp/sneep_prtd.$$
}

# Caching prtdiag interface
prtdiagf()
{
 $tracing && set -x
 pf_args="$*"
	
 # prtdiag doesnt work in a non-global zone
 ngzone && return 1

 # Mmake sure that we have a path to an executable
 if $SIMULATION
 then
  prtdiag=prtdiag_sim
 else
  for prtdiag in /usr/sbin/prtdiag /usr/platform/$UNAME_I/sbin/prtdiag
  do
   test -x $prtdiag && break
  done

  # No prtdiag ?. Return success above instead of failure so that caller does
  # not think that prtdiag is present, but failing
  test -x $prtdiag || return 0
 fi

 # Get prtdiag data and cache it
 # Check for repeated request for data
 # Convert args into simple string used as cache tag, (avoid space and dash)
 # This cache tag allows us to verify that the cache has the desired contents
 #
 # Tag is stored in first line of cache. Status is stored in second line of
 # cache. Data follows.
 pf_ctag=`echo x"$pf_args" | tr ' -' '@_' `

 # Make cache if necessary
 if [ -n "$prtdiag_cache" -a ! -r "$prtdiag_cache" ]
 then
  # Create empty cache with non-matching tag "XXX"
  touch "$prtdiag_cache" 2> $NULLFILE &&
  chmod 644 "$prtdiag_cache" 2> $NULLFILE &&
  echo XXX > "$prtdiag_cache" &&
  echo 0 >> "$prtdiag_cache"   &&
  echo "" >> "$prtdiag_cache"
 fi

 # Get old arg tag from cache
 if test -f "$prtdiag_cache"
 then
  # Get tag from line 1
  pf_oldtag=`sed -n 1p "$prtdiag_cache" `
 else
  # Just in case cache wasnt created make sure that we will not try to use it
  # Forced nonmatch
  pf_oldtag="YYY"
 fi

 # If old tag and new tag match, we should be able to reuse cache contents
 if test "$pf_ctag" != "$pf_oldtag"
 then
  : No match on cache tag

  # During system startup, sometimes picld is too busy to respond to prtdiag
  # for a while, so allow for slow startup of picld by retrying
  # Note that we have seen that the first data available when picld starts is
  # not always complete, but that is all we have unless we wait unknown
  # additional amount
  # We do not know if this will affect the serial data on platforms providing
  # it in prtdiag

  # At system startup we can at most have 9 tries to get picl ready
  # For manual sneep or if picld already running we will allow only
  # one try to get prtdiag output
  max_retry=9
  if [ -t 0 ] || picld_running
  then
   max_retry=1
  fi
  pf_tries=1
  while [ "$pf_tries" -le "$max_retry" ]
  do
   # Capture prtdiag output & error message in pf_data and status in pf_status
   pf_data=`$prtdiag "$@" 2> $NULLFILE `
   pf_status=$?

   # If prtdiag succeeds, no need of looping further
   if [ "$pf_status" -eq 0 ]
   then
    break
   fi

   # If prtdiag fails, try to find out root cause and log error message
   # We assume that, if header "Chassis Serial Number" is present in prtdiag
   # output, serial number is also present (based on testing of T5220)
   pf_serial=`echo "$pf_data" | grep -i "Chassis Serial Number"`
   pf_picl_err=`echo "$pf_data" | grep -i "picl_initialize failed:"`

   # If problem is due to picl initialization
   if [ -n "$pf_picl_err" ]
   then
    log -e picl daemon unresponsive : attempt $pf_tries of $max_retry

    # If serial number is found, come out of loop
    if [ -n "$pf_serial" ]
    then
     log $pf_picl_err
     break
    else
     # At system start up, try at most 9 times
     if [ "$pf_tries" -lt "$max_retry" ]
     then
      sleep `expr 8 \* $pf_tries`
     else
      # All retries exhausted, picl is still not ready
      log -e Cannot get data from prtdiag because hardware platform \
       information is not available
      log -e Investigation of possible problem with picld or hardware \
       platform is recommended
     fi
    fi
   else

    # Failure due to some cause other than picl initialization
    # Check for hardware/system specific failure messages in prtdiag output
    if echo "$pf_data" | egrep -i \
     "No failures found in System|No Hardware failures found in System" \
     > $NULLFILE
    then
     log -e Unknown prtdiag error, no specific hardware failure indicated by \
      prtdiag
    else
     # Hardware/system specific failure message found in prtdiag output
     log -e Prtdiag error, possible hardware or configuration problem
    fi
    break
   fi
   pf_tries=`expr $pf_tries + 1`
  done

  # Load the cache
  if test -w "$prtdiag_cache"
  then
   # Show tag, status and data
   echo "$pf_ctag" > "$prtdiag_cache"
   echo "$pf_status" >> "$prtdiag_cache"
   echo "$pf_data" >> "$prtdiag_cache"
  fi
 fi
 if test -r "$prtdiag_cache"
 then
  : Return cache file contents except tag and status
  # Status from line 1
  pf_status=`sed -n 2p "$prtdiag_cache" `

  # Data starts at line 3
  sed 1,2d "$prtdiag_cache"
  return $pf_status
 else
  : Cannot read cache file,  try variables
  if test ! -z "$pf_data"
  then
   # Return data
   echo "$pf_data"

   # Return status
   return $pf_status	
  fi
 fi

 # Fail - No cache or data
 return 1
}

# Check if picld deamon is running
picld_running()
{
 $SIMULATION && [ -n "$SIMULATION_PICLD_STATUS" ] && \
  return $SIMULATION_PICLD_STATUS
 if [ -x /usr/bin/svcs ]
 then
  [ "`/usr/bin/svcs -Ho STA svc:/system/picl:default`" = ON ]
 else
  /usr/bin/ps -eo comm | /usr/bin/egrep -s '(^|/)picld$'
 fi
}

#
#	Main prtdiag module
#
prtdiag_info()		# CSN from prtdiag if possible
{
	pi_action="$1"			# get/set action
	pi_tag="${2:-$SERIAL_TAG}"	# optional tag
	pi_val="$3"	# value for tag  # optional value

	case "$pi_action" in
	"")	errmsg Internal error: no action for prtdiag .
		return 1
		;;
	set)
		# cannot set value in prtdiag - read-only
		# but they dont have to know that
		return 0	# update succeeded
		;;
	get)
		#
		# Retrieve value for tag from the prtdiag
		#

		# only  SERIAL_TAG is possible
		test "$pi_tag" = $SERIAL_TAG || return 1


		# A change was made for Solaris 10 which was intended for the
		# Sun-Fire 445, and 215/245
		# but may be used on other platforms :

		# "The feature was integrated into Nevada snv_30.
		#  It will also be backported to S10U2. "
		# It provides serial data from the ALOM
		# in the output of "prtdiag"

		# # prtdiag -v | tail
		# /pci@1f,700000         pciex108e,80f0   okay               3
		#
		# System PROM revisions:
		# ----------------------
		# OBP 4.x.seattle.bin 2005/12/21 02:47 Sun Fire V215/V245
		# POST 4.120.48 2005/12/19 16:40
		#
		# Chassis Serial Number:
		# ----------------------
		# 12345ABCDE    (or whatever it might really be)

		# get serial from prtdiag data
		#	look for serial number in prtdiag data
		#	on the second line after the heading
		#	heading.>	|Chassis Serial Number:
		#	line 1->	|----------------------
		#	line 2->	|12345ABCDE


		pi_serial=` prtdiagf -v |
			nawk ' /Chassis Serial Number/ {
					getline ;	# skip "-----"
					getline ;	# line with serial
					print $1	# print serial
			    	}
				{ next }	# consume extra input
						# to avoid broken pipe
			'
		`   # end pi_serial=

		# fail if prtdiag has no serial data for platform
		test -z "$pi_serial" && return 1

		echo "$pi_serial"	# return value
		return 0		# return success
		;;
	esac
	return 0	# should never get here
}

# -- End of Prtdiag interface

##########################################################
#
# SMBIOS interface for chassis serial data
#
# CSN from smbios if possible
smbios_info()
{
 # Get/set action
 smb_action="$1"

 # Optional tag
 smb_tag="${2:-$SERIAL_TAG}"

 # Value for optional tag
 smb_val="$3"	
 case "$smb_action" in
  "")
   errmsg Internal error: no action for smbios .
   return 1
   ;;
  set)
   # Cannot set value in smbios - read-only
   return 0
   ;;
  get)
   # Retrieve value for tag from the smbios

   # *Some* X86 platforms support the SMBIOS standard for detailed hardware
   # information

   # Even if serial data is present, it may not be an accurate chassis serial
   # and the user may want to override it in eeprom

   # Select Chassis info using ID 1 or 3 from standard
   #   type 1 : SMB_TYPE_SYSTEM (system information)
   #   type 3 : SMB_TYPE_CHASSIS (system enclosure or chassis)

   #$ smbios -t 1
   #ID    SIZE TYPE
   #1     89   SMB_TYPE_SYSTEM (system information)
   #
   #  Manufacturer: TOSHIBA
   #  Product: TECRA M5
   #  Version: PTM51U-05W01P
   #  Serial Number: 86078428H
   #
   #  UUID: 6edcb700-2ab4-11db-8051-b05d86078428
   #  etc.

   #$ smbios -t 3
   #ID    SIZE TYPE
   #3     56   SMB_TYPE_CHASSIS (system enclosure or chassis)
   #
   #  Manufacturer: TOSHIBA
   #  Version: Version 1.0
   #  Serial Number: 00000000
   #  Asset Tag: 0000000000
   #
   #  OEM Data: 0x0
   #  Lock Present: N
   #  Chassis Type: 0xa (notebook)
   #  etc.

   if $SIMULATION
   then
    # Use sim function above
    smbios=smbios_sim
   else
    # Set up $smbios to find a useful executable
    for smbios in /usr/sbin/smbios /sbin/smbios /usr/bin/smbios
    do
     test -x $smbios && break
    done
    test -x $smbios || return 1
   fi

   # Look for different things depending on tag
   case "$smb_tag" in
    $SERIAL_TAG)
     smb_pattern='Serial Number:'
     ;;
    model)
     # Pseudo-tag to get system model
     smb_pattern='Manufacturer:|Product:|Version:'
     ;;
    *)
     # No other tags are useful
     return 1
     ;;	
   esac
   smb_val=` (
			$smbios -t 1  2> $NULLFILE # try SMB_TYPE_SYSTEM first
			$smbios -t 3  2> $NULLFILE # try SMB_TYPE_CHASSIS 2nd
						# just in case, try anything
			$smbios 2> $NULLFILE # (have seen serial on type 256)

			) | show_dataflow | nawk -v pat="$smb_pattern" '

				done ==1 { next }	# consume extra lines

				#ID    SIZE TYPE
				#nnn   nnn  SMB_SOMETHING (description)
				/^ID.*SIZE/ {
					# new section,
					# have not seen SMB_* line yet
					next_match = 0 ;

					# if we printed something, then this
					# is the next section and there is
					# no more to be gathered.  Done.

					if (printed) done=1
				}

				# start looking when we have seen a useful TYPE
				/SMB_TYPE_SYSTEM/	{ next_match = 1 }
				/SMB_TYPE_CHASSIS/	{ next_match = 1 }

				# if we are looking, and we find what we want,
				# and its not just empty or zero or none,
				# then print it and ignore everything else.
				$0 ~ pat && next_match == 1 {
					# remove the matched words from line
					gsub(pat,"");

					# remove uninteresting results

					#	Not Available
					sub(/[Nn]ot [Aa]vail.*/, "", $0);
					#	To Be Filled By O.E.M.
					sub(/[Tt]o [Bb]e.*/, "", $0);
					#	None
					sub(/[Nn]one*/, "", $1);
					# ethernet MAC address
					#	00:14:4F:20:D3:48
					sub(/.*:.*:.*:.*:.*:.*/, "", $1);

					# print anything interesting
					if ($1 !~ /^$|^0$|^0[-0]*$/) {
						print $0

					# we want to exit now, but if we do
					# we get a Broken Pipe, so we have to
					# consume the remaining input
						printed = 1;
					
					}
				}

				{ next }	# avoid printing every line
			' -  # end nawk
		`   # end smb_val=

   # Remove leading and trailing whitespace, join lines
   smb_val=`echo $smb_val`

   # Fail if smbios has no data for this platform
   test -z "$smb_val" && return 1

   # Return value, converting whitespace to underline
   echo "$smb_val" | $tr -s '[:blank:]' '[_*]'
   return 0
   ;;
 esac
 return 0
}

##########################################################
#
# IPMI interface for product / chassis serial data
#
ipmi_info()    # PSN from ipmi if possible
{
 IPMI_ACTION="$1"                # Get/set action
 IPMI_TAG="${2:-${SERIAL_TAG}}"  # Optional tag
 IPMI_VAL="$3"                   # Value for tag - Optional value

 case "${IPMI_ACTION}" in
  "")
   errmsg Internal error: no action for ipmi .
   return 1
   ;;
  set)
   # Cannot set value in ipmi - read-only but they dont have to know that
   return 0  # Update succeeded
   ;;
  get)
   #
   # Retrieve value for tag from ipmi
   #

   # *Some* X86 platforms support the IPMI standard
   # for detailed hardware information.

   # Even if serial data is present, it may not be an accurate
   # chassis serial and the user may want to override it in eeprom

   if ${SIMULATION}
   then
    IPMITOOL=ipmi_sim  # Use sim function above
   else
    # Set up ${IPMITOOL} to find a useful executable
    for IPMITOOL in /opt/ipmitool/sbin/ipmitool \
                    /opt/ipmitool/bin/ipmitool \
                    /usr/sfw/bin/ipmitool \
                    /usr/sbin/ipmitool \
                    /sbin/ipmitool \
                    /usr/bin/ipmitool
    do
     test -x "${IPMITOOL}" && break
    done
    test -x "${IPMITOOL}" || return 1
   fi

   # Look for different things depending on tag
   case "${IPMI_TAG}" in
    ${SERIAL_TAG} )
     IPMI_PATTERN='Serial'
     ;;
    model )  # Pseudo-tag to get system model
     IPMI_PATTERN='Name'
     ;;
    * )
     return 1 ;; # No other tags are useful
   esac

   IPMI_VAL=` (
     ${IPMITOOL} -I bmc fru 2> ${NULLFILE} | show_dataflow |
       nawk -F : -v pattern="${IPMI_PATTERN}" '

       BEGIN {
        skipping = 1
        chassispattern = "Chassis " pattern;
        productpattern = "Product " pattern;
        versionpattern = "Product Version";
        value = ""
       }

       # Start looking when we see chassis or motherboard
       skipping && /Chassis Type/ {
        # Chassis info not preceeded by recognized Product Description
        skipping = 0
        type="misc"
       }

       # Look for ILOM version in Builtin FRU Device
       # FRU Device Description : Builtin FRU Device (ID 0)
       #  Board Mfg Date        : Sun Dec 31 23:00:00 1995
       #  Board Serial          : 00:1e:68:c6:54:6b
       #  Board Part Number     : AST2000
       #  Product Manufacturer  : Sun Microsystems
       #  Product Name          : ILOM
       #  Product Version       : ********
       /FRU.*Desc.*: *.*ID 0[)]/ {
        skipping = 0
        type="ILOM"
       }

       # Motherboard Device Description
       #     FRU Device Description : MB (ID 2)
       #     FRU Device Description : mb.fru (ID 2)
       # Recent analysis showed that the ID for motherboard may not be 2
       # necessarily. Hence making the regular expression more generic
       /FRU.*Desc.*: *[Mm][Bb].[Ff][Rr][Uu] |FRU.*Desc.*: *[Mm][Bb] / {
        skipping = 0
        type="motherboard"
       }

       # System Device Description
       #     FRU Device Description : /SYS (ID 3)
       #     (System ID may be other than 3)
       # also
       #     FRU Device Description : System FRU (ID 1)
       #      Product Manufacturer  : HP
       #      Product Name          : ProLiant DL185 G5
       #      Product Serial        : USE925N9KH
       /FRU.*Desc.*:[ \/]*SYS |FRU.*Desc.*: *System FRU / {
        skipping = 0
        type="system"
       }

       # Blade chassis
       # FRU Device Description : MIDPLANE (ID 70)
       #  Chassis Type          : Rack Mount Chassis
       #  Product Manufacturer  : SUN MICROSYSTEMS
       #  Product Name          : SUN BLADE 6000 MODULAR SYSTEM
       #  Product Serial        : 0930BD16D3
       /FRU.*Desc.*: *MIDPLANE .*ID/ {
        skipping = 0
        type = "midplane"
       }

       # Blank line separates IPMI data blocks
       /^$/ { skipping =1 } # End of block

       skipping == 1 { next }  # Skip this input line

       $1 ~ productpattern {
        value = $2
        fulltype = type "_product";
       }

       $1 ~ chassispattern {
        value = $2
        fulltype = type "_chassis";
       }

       $1 ~ versionpattern {
        value = $2
        fulltype = type "_version";
       }

       value != "" {
        # Found something like what we are looking for
        sub("^  *","", value); # Remove leading white
        sub("  *$","", value); # Remove trailing white

        # Save anything interesting
        if (value !~ /^$|^0$|^0[-0]*$/) {
         if ( found[fulltype] == "")
          found[fulltype] = value;
        }
        value = ""
       }
       END {
        # Output highest priority data first
        if (found["ILOM_version"] ~ /^3./)
        {
         # ILOM 3 contains correct product serial, so prioritize it higher
         if (found["system_product"])
          print found["system_product"];
         if (found["system_chassis"])
          print found["system_chassis"];
         if (found["midplane_product"])
          print found["midplane_product"];
         if (found["midplane_chassis"])
          print found["midplane_chassis"];
        }
        else
        {
         # ILOM 2 initially had product serial in midplane and incorrect
         # serial for System/Product Serial, so prioritize midplane higher
         if (found["midplane_product"])
          print found["midplane_product"];
         if (found["midplane_chassis"])
          print found["midplane_chassis"];
         if (found["system_product"])
          print found["system_product"];
         if (found["system_chassis"])
          print found["system_chassis"];
        }
        if (found["motherboard_product"])
         print found["motherboard_product"];
        if (found["motherboard_chassis"])
         print found["motherboard_chassis"];
        if (found["misc_product"])
         print found["misc_product"];
        if (found["misc_chassis"])
         print found["misc_chassis"];
       }
     ' -
   ) `  # End IPMI_VAL=

   # Fail if ipmi has no data for this platform
   test -z "${IPMI_VAL}" && return 1

   # Clean up any known problems with IPMI serial
   # blade servers especially may have prefix
   #	  prefix '0111APO-' sun_bt6320
   #	  prefix '1005LCB-' sun_bt6340

   # Some serials are really MAC address
   #	  Product Serial  : 00:14:4F:26:DD:9D
   # but only first octet (2 chars) gets through from nawk -F : when value is
   # from field 2 (above). Drop serial which is just 2 hex digits
   IPMI_VAL=`echo "${IPMI_VAL}" |
             sed -e 's/^[0-9][0-9][0-9][0-9][ABCDEFGHIJKLMNOPQRSTUVWXYZ][ABCDEFGHIJKLMNOPQRSTUVWXYZ][ABCDEFGHIJKLMNOPQRSTUVWXYZ]-//' \
                 -e '/^[0-9ABCDEFabcdef][0-9ABCDEFabcdef]$/d'
   `

   # Return value, converting squeezed whitespace to underline
   IPMI_VAL=`echo "${IPMI_VAL}" | "${tr}" -s '[:blank:]' '[_*]'`
   # Ipmi may return multiple different serial number.
   # Take the first and highest priority one.
   echo "${IPMI_VAL}" | head -1
   return 0  # Return success
   ;;
 esac
 return 0  # Should never get here
}

##########################################################
#
# prtconf interface for chassis serial data
#
prtconf_info()		# CSN from prtconf if possible
{
	pcf_action="$1"			# get/set action
	pcf_tag="${2:-$SERIAL_TAG}"	# optional tag
	pcf_val="$3"	# value for tag  # optional value

	case "$pcf_action" in
	"")	errmsg Internal error: no action for prtconf .
		return 1
		;;
	set)
		# cannot set value in prtconf - read-only
		# but they dont have to know that
		return 0	# update succeeded
		;;
	get)
		#
		# Retrieve value for tag from prtconf
		#

		# only  SERIAL_TAG is possible
		test "$pcf_tag" = $SERIAL_TAG || return 1


		# make sure that we have a path to an executable
		if $SIMULATION ; then
			prtconf=prtconf_sim  # use sim function above
		else
			# set up $prtconf to find a useful executable
			for prtconf in /usr/sbin/prtconf /sbin/prtconf
			do
				test -x $prtconf && break
			done
			test -x $prtconf || return 1	# no prtconf?
		fi

		#  explorer sysconfig/prtconf-vp.out
		#
		#    from 'Sun SPARC Enterprise M9000 Server'
		#	System Configuration:  Sun Microsystems  sun4u
		#	Memory size: 32768 Megabytes
		#	System Peripherals (PROM Nodes):
		#	
		#	Node 0xf002a074
		#	    boot-retained-page:
		#	    cpu-signature-ranges:  00004081.f132e000.000000..
		#	    chassis-sn:  'AKD0736110'
		#	    banner-name:  'Sun SPARC Enterprise M9000 Server'
		#	    model:  'DC3'
		#	    name:  'SUNW,SPARC-Enterprise'
		#	    device_type:  'jupiter'
		

		#     from Enterprise 450
		#	(OBP variable)
		#	system-board-serial#:  '946M3222'
		#  Note: this is the chassis serial,
		#	not the system-board-serial,
		#	 no matter what it says.

		#	from Enterprise 250
		#	system-board-serial# '5014681011959'
		# Note:  this is as it says, the system-board-serial.
		#	Now we have to figure out which is which....
		

		# pull out second field of interesting line, drop quotes
		#	note that prtconf lines can be extremely long
		#	and can cause nawk to fail, so we fold them first.

		pcf_serial=`$prtconf -vp 2> $NULLFILE | show_dataflow |
		    fold -w 255 |  nawk -v sqot="'"  '
			$1 == "chassis-sn:" { ser = $2 }
			$1 == "system-board-serial#:" { ser = $2 }
			END {
				gsub(sqot, "", ser);	# remove single quotes

				# fail if no serial
				if (ser == "") exit 1;
			
				# fail if string has strange contents
				#	(anything not alphanumeric)
				if (ser ~ /[^abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0-9]/) exit 1;

				# Might be a board serial
				# Reject if it is long and starts with 501
				#  501 starts many Sun501 part numbers
				if (length(ser) > 10 && (ser ~ /^501/))
					exit 0;

				# seems to be OK
				print ser;
			} '  -
		`  # end pcf_serial=

		# fail if prtconf has no serial data for platform
		test -z "$pcf_serial" && return 1	# no serial found

		echo "$pcf_serial"	# return value
		return 0		# return success
		;;
	esac
	return 0	# should never get here	
}

# -- CST Configuration Service Tracker Interface functions

# Where to find CST data and config
CSTVAR=${CSTVAR:-$SIMBASE/var/opt/SUNWcst}

# Serial stored in this file by CST
CSTUSERDATA=$CSTVAR/user_data

# Return 0 if CST is installed and usable.
# Also return package install dir (generally /opt)
cst_available()
{
 for cst_pkg in SUNWcstu SUNWcstv SUNWcstr SUNWcst
 do
  if CSTBASE=`$pkginfo -r $cst_pkg 2> $NULLFILE `
  then
   echo $CSTBASE

   # Assume CST is up - app_event can tell if it is down later
   return 0
  fi
 done

 # Cannot find or use CST
 return 1
}

: get_cst_serial

# Get serial from CST user data if possible
get_cst_serial()
{

 # No real need to check for CST installed- just look for user_data
 # gcs_dir=`cst_available` || return 0	# Keep quiet if no CST

 # Fail if we cannot read it
 test -r "$CSTUSERDATA" || return 1	

 # User_data file is tab separated, serial number is on a line like this:
 # Serial # of System<tab>819fc481
 if gcs_serline=`grep -i "serial.*system" $CSTUSERDATA `
 then

  # Return serial number
  echo "$gcs_serline" | cut -sf 2

  # Snd success
  return 0
 fi

 # Failed to get serial from CST
 return	1
}

: set_cst_serial

# Send CST event with serial number.
# To be picked up and used to set CST user data.
set_cst_serial()
{
 $tracing && set -x

 # New serial number is pased in
 scs_serial="$1"

 # If this is an explorer-based simulation, there will be no CST user data file
 # and we dont really want to create one inside the explorer, and we dont want
 # to try sending a CST event. We also dont want any unnecessary errors.
 # Bail out successfully
 $explorer_sim && return 0

 # As of 200411, the only agent-side changes to user data which are propagated
 # to the CST middleware are those made using the GUI.
 #
 # Note: By using the CST User Data Editor (uded) available from Sun Field
 #       Customer Advocacy, edits made to the user data on the agent CAN be
 #       propagated to the middleware. uded will also automatically detect and
 #       leverage the application events described below.
 #
 # As a result, without uded there is little value in editing the user_data
 # file as we edit the explorer config file.
 #
 # The "value" is that if the serial number is erased, and then requested again
 # via sneep, it will be unknown. If we do not edit it out of the file, then
 # the old serial is returned again. This can come as a surprise.
 #
 # Our most valuable CST activity then, is to create a special application
 # event on the agent which is passed to the middleware and can be processed
 # later to create a simulated user_data edit if desired.
 # (this in handled in conjunction with the "uded" utility, not part of the
 # standard CST installation)

 # First, update local cst user data file whether or not CST is running
 # Update local file with serial
 update_cst_udata "$scs_serial"	

 # Send CST event to record serial locally, and on middleware server
 # Check for usable CST. Keep quiet if no CST, complain if CST not working
 scs_dir=`cst_available` || return 0

 # Check for CST app_event utility installed correctly
 scs_appev=$scs_dir/SUNWcstu/bin/app_event
 if test ! -x $scs_appev
 then
  errmsg CST app_event utility not found .
  return 1
 fi

 # Send CST app_event of type "Serial Number" with new value.
 # Capture output, as we have found the app_event exit value unreliable.
 test -z "$scs_serial" && scs_serial="deleted"
 scs_tmp=`$scs_appev "Serial Number" "<CSN>$scs_serial</CSN>" 2>&1 `
 scs_stat=$?

 # Check app_event output to see if all went OK
 if echo "$scs_tmp" | grep -i DONE > $NULLFILE
 then
  : DONE seems to mean CST is OK
 else
  errmsg CST may be down .
  return 1
 fi
 return 0
}

# Update CST user_data file
update_cst_udata()
{
 # Pass in the serial number to update in the file
 ucu_serial="$1"

 # See set_cst_serial for why we bail on an explorer simulation
 $explorer_sim && return 0
 if test ! -f $CSTUSERDATA
 then

  # There is no CST user file (perhaps no CST directory) on this system.
  # If CST is installed, we will update the user file anyway.
  # If CST is not installed, return success anyway because CST is optional.
  $pkginfo -q SUNWcstu || return 0
		
  # File missing, but CST is installed.
  # Create the missing file and set the serial in it
  # RFE 00056 (extension): update missing serial

  # Create CST data directory if missing
  ucu_dir=`dirname $CSTUSERDATA`
  test -d $ucu_dir || mkdir -m 0755 -p $ucu_dir 2> $NULLFILE

  # Create empty user data file, or return
  # Return success because CST is not required
  touch $CSTUSERDATA || return 0

  # Set safe permissions
  chmod 644 $CSTUSERDATA 2> /dev/null
 fi

 # If the old and new values are the same (even if null) then no need to update
 ucu_oldserial=`get_cst_serial`
 test "$ucu_serial" = "$ucu_oldserial" && return 0

 # User_data file is tab separated, serial number is on a line like this:
 # Serial # of System<tab>819fc481
 # Save everything EXCEPT the line containing the Serial entry
 ucu_otherlines=`grep -v -i "serial.*system" $CSTUSERDATA `
 if touch $CSTUSERDATA 2> $NULLFILE
 then
  : No problem
 else
  errmsg Cannot write $CSTUSERDATA as $thisuser .
  return 1
 fi

 # Put new value (or null value) into tab-separated file
 echo 'Serial # of System'"$tab$ucu_serial" > $CSTUSERDATA || return 1

 # Put the other entries into the file
 echo "$ucu_otherlines" >> $CSTUSERDATA || return 1
 log Changed Serial Number in CST user data $CSTUSERDATA \
  from \"$ucu_oldserial\" to \"$ucu_serial\"
 return 0
}

# -- End of CST Configuration Service Tracker Interface functions

: get_value

# Get value from data source(s) and report on stdout
get_value()
{
 # First param is whether or not this is serial number
 # Eeprom tag should be passed in
 gv_tag="${1:-$DESIREDTAG}"

 # Sources to check in sequence
 gv_sources=${2:-$SNEEPPATH}

 # Assume success
 gv_status=0

 # True if value has been found
 gv_found=false

 # Process $gv_tag
 case "$gv_tag" in
  "$SERIAL_TAG" | serial | ser | csn | CSN | psn | PSN)

   # Use standard tag
   gv_tag="$SERIAL_TAG"

   # Processing a serial number
   gv_serial=true
   ;;
  *)

   # Not the serial number
   gv_serial=false
   ;;
 esac
 for gv_src in `(IFS=: ; echo $gv_sources)` default
 do
  : ; : check $gv_src ; : ;

  # Printable source name
  gv_sourcename=$gv_src
  case "$gv_src" in
   fruid | FRUID | fru* | FRU*)

    # Check $gv_serial before lookup
    : $gv_src not implemented yet
    ;;
   prtdiag* | PRTDIAG*)
    gv_value=`prtdiag_info get "$gv_tag" `
    ;;
   prtconf* | PRTCONF*)
    gv_value=`prtconf_info get "$gv_tag" `
    ;;
   sms | SMS)

    # Check $gv_serial before lookup
    gv_value=`sms_info get "$gv_tag" `
    ;;
   eeprom | EEPROM | eep* | EEP* | nvram* | NVRAM*)
    gv_value=`get_eeprom_value "$gv_tag" `
    ;;
   backup | bac*)
    gv_value=`backup get "$gv_tag" ""`
    gv_sourcename="$gv_src : $BACKUP"
    ;;
   explorer | exp*)
    # Check $gv_serial before lookup
    $gv_serial && gv_value=`get_explorerconfig_serials`
    ;;
   cst | CST)

    # Check $gv_serial before lookup
    $gv_serial && gv_value=`get_cst_serial `
    ;;
   smbios | SMBIOS)

    # Check $gv_serial before lookup
    gv_value=`smbios_info get "$gv_tag" `
    ;;
   ipmi | IPMI)

    # Check $gv_serial before lookup
    gv_value=`ipmi_info get "$gv_tag" `
    ;;
   fserial | FSERIAL)

    # Get value from fujitsu
    $gv_serial && gv_value=`fserial_info get "$gv_tag"`
    ;;
   prtpicl | PRTPICL)

    # Get value from prtpicl
    gv_value=`prtpicl_info `
    ;;
   default | def*)

    # Provide default value (unless value has already been found
    # as it might be with -a option to report all sources)
    if $gv_found ; then
     : skipping default because already found
    else
     gv_value=$defaultval
     gv_sourcename="default value"

     # Fail if serial cannot be determined
     gv_status=1	
    fi
    ;;
   internal)

    # Internally fabricated pseudo-tags
    case "$gv_tag" in
     model)
      gv_value=`get_model`
      ;;

     # Hostname and hostid probably never get past "get_multi", just in case
     hostid)
      gv_value=`get_hostid`
      ;;
     hostname)
      gv_value=`get_hostname`
      ;;
    esac
    ;;
   *)
    errmsg Unknown data source: \""$gv_src"\" .
    ;;
  esac

  # Do any required processing on the value returned from each data source
  case "$gv_tag" in
   model)

    # Filter most "model" strings
    case "$gv_src" in
     eeprom | backup)
      # Stores overrides
      : User allowed control over model
      ;;	

     *)
      : Filter model returned from $gv_tag
      gv_value=`echo "$gv_value" | model_filter2`
      ;;
    esac
    ;;
  esac

  # Report when we find a value
  if test ! -z "$gv_value"
  then
   gv_found=true
   if [ "$gv_sourcename" = "explorer" ]
   then
    (IFS='|'; for FIL_SER in $gv_value
    do
     FIL=`echo ${FIL_SER} | cut -d'=' -f1`
     SER=`echo ${FIL_SER} | cut -d'=' -f2`
     $verbose &&	echo "   " "$gv_tag" from $gv_sourcename : ${FIL} :
     echo "${SER}"
     $allsources || break
    done)
   else
    $verbose &&	echo "   " "$gv_tag" from $gv_sourcename :
    echo "$gv_value"
   fi
   gv_value=""

   # Stop as soon as we find a value unless we are reporting all sources
   $allsources || break
  fi
 done
 return $gv_status
}

: showplat

# Emulate showplatform -p csn output
showplat()
{
 serial="$@"	# pass in serial number
 cat << END
     CSN:
     ====
     Chassis Serial Number: $serial
END
}

: set_value

# Set value in eeprom, explorer config files, CST
# We allow null serial so that it can be removed
set_value()
{
 # Eeprom tag to use
 SV_TAG="$1"

 # Value to set
 SV_VALUE="$2"

 # Reject illegal tags
 sane_tag "$SV_TAG" || return 1

 # Assume tag is not for serial number
 SV_SERIAL=false

 # Assume no need to change value case
 SV_FORCE_UCASE=false

 # Get the default value for this tag
 case "$SV_TAG" in
  hostname)
   # Default is true hostname
   SV_DEFAULT=`get_hostname`
   ;;
  hostid)
   # Default is true hostid
   SV_DEFAULT=`get_hostid`
   ;;
  *)
   # All other tags use the same default
   # Usually "unknown" but settable with -d
   SV_DEFAULT="$defaultval"
   ;;
 esac

 # Any special handling or policy for this tag?
 case "$SV_TAG" in
  "$SERIAL_TAG" | serial | ser | csn | CSN | psn | PSN)
   SV_SERIAL=true # Setting serial number
   SV_FORCE_UCASE=$force_ucase # Take user view of case
   SV_TAG=$SERIAL_TAG # Use standard tag
   ;;

  # 6842161 Hostname and hostid need not have the "set" permission
  # Setting hostname/hostid is no longer allowed, but if an attempt is made
  # to set the default value (the true hostname/hostid) we let it pass to
  # avoid an error message. Because it is the default value, it gets erased
  # later. Once erased, 'sneep start' wont send it again.
  # An attempt to set any other value is not allowed and fails with an error.
  hostname | hostid )
   case "$SV_VALUE" in
    "")
     # Erase tag, report error if missing
     : User is deliberately erasing $SV_TAG
     ;;
    "$SV_DEFAULT")
     # When user try to set actual hostname or hostid,
     # sneep tries to erase it from eeprom and/or backup.
     # If this tag is not already present sneep throws an error message,
     # so we will return from here if the tag is missing.
     : User is setting $SV_TAG to default value $SV_DEFAULT

     # Method get_value allows us to check both eeprom and backup for tag
     # but returns $defaultval if not found.  Use return status
     if get_value "$SV_TAG" eeprom:backup > /dev/null
     then
      : found the value in eeprom/backup, OK to erase
     else
      : $SV_TAG not in eeprom/backup - dont try to erase

      # A query for this tag will return this value, so pretend this attempt
      # was successful
      return 0
     fi
     ;;
    *)
     : User is setting $SV_TAG to non-default value $SV_VALUE
     errmsg To avoid inconsistencies, it is not possible to set tag \"$SV_TAG\"
     errmsg , but tag \"$SV_TAG\" can still be used to obtain this information
     errmsg from the system.

     # Failed to set user value
     return 1
     ;;
   esac
   ;;
  *)
   # Not a special tag - set specified user value
   ;;
 esac

 # Do not allow setting the value for the tag to default value. The one which
 # comes back when the value is not set (normally "unknown" but it can be set
 # with -d ) This is sometimes attempted accidentally when using
 # 'sneep -Tv | sh'  (as is done in startup/recovery mode) as if serial is not
 # set, 'sneep -Tv' will produce 'sneep -t ChassisSerialNumber -s unknown'
 # Instead set value to null to remove the tag, and subsequent queries will
 # return the desired value if all else is the same. (There are cases involving
 # changes to SNEEPPATH or the default value in which subsequent queries might
 # not return the default value, but the alternatives are probably worse:
 # Refusal to set the value, or setting it and getting UNKNOWN back)

 # Convert setting of default value into erase
 if test "$SV_VALUE" = "$SV_DEFAULT"
 then
  : Convert set with default value into erase for $SV_TAG

  # Eliminates old, potentially bad data, saves space in eeprom,
  # query for $SV_TAG still returns correct (default) value
  SV_VALUE=""
 fi
 if $SV_FORCE_UCASE
 then

  # Need a standard to simplify comparisons everywhere else.
  # I choose upper case - Hostids are different - they are hex numbers.
  # so that leading whitespace goes away and multiple lines get joined
  SV_VALUE=`echo $SV_VALUE | $tr '[:lower:]' '[:upper:]' `
 fi

 # Check if we are setting a serial number, sanity check the contents
 if $SV_SERIAL
 then
  if sane_serial "$SV_VALUE"
  then
   : no problem
  else
   errmsg Cannot use this value for serial number.
   return 1
  fi
 else

  # Not setting a serial number
  # If value is null, then we are deleting it and that is always permissible
  if test -z "$SV_VALUE"
  then
   : deleting $SV_TAG
  else
   : setting a non-serial tag

   # We used to refuse to set value if Serial is not already set. This policy
   # is intended to help make certain that eeprom.out contains serial even on
   # x86 where only 1 entry is allowed, and also ensures that sneep serves its
   # primary function first rather than just being a handy utility that is used
   # for other purposes.
   # However, the one-line problem in the x86 eeprom has been overcome, and
   # more importantly, this causes difficult situations occur on occasion when
   # tags are being added or removed. Better to overlook it now.
  fi	
 fi
 if set_eeprom_value "$SV_VALUE" "$SV_TAG"
 then
  # Eeprom update succeeded
  : no problem
  SV_EEFAILED=false
  SV_STAT=0
 else
  # Eeprom update failed
  errmsg Could not save $SV_TAG in system EEPROM as $thisuser .
  SV_STAT=1
  SV_EEFAILED=true
 fi

 # Only update CST if this is for a serial number and we were able to set
 # it in EEPROM, or if we are updating a serial number, EEPROM update
 # failed, new serial number is not null and old serial number from EEPROM is
 # null/EEPROM unreadable.
 SV_RESTART_PSN=false
 if ($SV_SERIAL && test $SV_EEFAILED = false) || ($SV_SERIAL &&
     test $SV_EEFAILED = true && test ! -z "$SV_VALUE" &&
     test -z "$se_oldval")
 then
  SV_RESTART_PSN=true
  for SV_SOURCE in `(IFS=: ; echo $SNEEPPATH)`
  do
   case "$SV_SOURCE" in
    cst)
     if set_cst_serial "$SV_VALUE"
     then
      : no problem
     else
      errmsg Could not send CST change event.
     fi
     ;;
    default)
     : no need to set "$SV_SOURCE"
     ;;
   esac
  done
 fi

 # Current policy is: Update backup file even if eeprom update failed to
 # overcome eeprom limitations. Could lead to surprises when values dont match,
 # but mostly does what you would want it to do
 # Note: set_eeprom_value knows that backup will be set here, so it does not
 # attempt to do it, which it would otherwise need to do in order to properly
 # virtualize the eeprom
 # If eeprom update failed and backup did not,
 # then notify the user so there is less surprise later.
 if backup set "$SV_TAG" "$SV_VALUE" && $SV_EEFAILED
 then
  errmsg ""
  errmsg Saved "$SV_TAG" in backup.
  errmsg ""
 fi
 if test "$SV_RESTART_PSN" = true
 then
  # CR 6726919 : restart SUNWpsn after updating serial
  # This will make serial number in PSN consistent with that in sneep
  restart_psn
 fi
 return $SV_STAT
}

: update_archive

# Updates current boot archive
update_archive()
{
 $tracing && set -x

 # Before we perform any update activity, check if bootadm exists for this host
 # we need not proceed further if bootadm is not installed
 if [ ! -x /usr/sbin/bootadm ]
 then
  return 1
 fi

 # Check if there was any attempt made to update  eeprom contents before we
 # update the current archive. Also, update the boot archive only if
 # solaris/bootenv.rc is listed in the boot archive
 if "$eeupdated" && bootadm list-archive 2>&1 | grep bootenv.rc >/dev/null
 then

  # We have all the necessity to update the current boot archive
  bootadm update-archive >/dev/null
  if [ $? -ne 0 ]
  then
   errmsg ""
   log -e The boot archive MUST be updated as the serial number was updated.
   log -e The boot archive update failed. Please run \
    \'bootadm update-archive\' manually.
   log -e Failure to do this could result in failure to boot cleanly later.
   return 1
  fi
 fi
 return 0
}

: sane_serial

# Succeed if the string passed in seems reasonable for a serial
sane_serial()
{
 $tracing && set -x
 ss_value="$1"

 # Serial must be alphanumeric and fairly short. Delete alphanumerics and see
 # what is left: should be nothing left (also allow hyphen and underscore)
 ss_other=`echo "$ss_value" | $tr  -d '[:alnum:]-_' `
 if test ! -z "$ss_other" -a $override = false
 then
  errmsg Serial Number must contain only alphanumerics.
  errmsg "  " -F option can force override
  return 1
 fi

 # When it gets this long, warn user at a minimum
 ss_len=`echo "$ss_value"'\c' | wc -c `
 if test $ss_len -gt 15
 then
  if test $ss_len -gt 20 -a $override = false
  then
   case "$ss_value" in
    VMware* | VMWARE*)
     : Valid VMware serial is very long
     override=true
     ;;
    *)
     : Not a special case - reject long serial
     errmsg Setting provided is too long for a serial number.
     errmsg "  " -F option can force override
     return 1
     ;;
   esac
  fi
  $override || errmsg \""$ss_value"\" is unusually long for a serial number.
 fi
 return 0
}

: sane_tag

# Succeed if the passed value is a reasonable tag value
sane_tag()
{
 $tracing && set -x

 # Get tag passed as argument
 st_tag="$1"

 # Special characters allowed in tags
 st_special="@#_+=-"

 # Must be alphanumeric and fairly short, with a few other characters allowed.
 # Delete the allowable characters and see if anything remains.
 st_other=`echo "$st_tag" | $tr  -d "[:alnum:]$st_special" `
 if test ! -z "$st_other"
 then
  if $override
  then
   log -e Safety override option has been used to permit special characters \
    in tag \"$st_tag\"
  else
   errmsg Only alphanumerics and \"$st_special\" are allowed in the tag.
   return 1
  fi
 fi
 return 0
}

: sneep_startup

# perform data integrity checks for system startup
# (assuming we have been called as a startup script)
sneep_startup()		# check serial integrity e.g. at system startup
{			# and fix if possible

	# check (eeprom) serial against other sources and complain of mismatch
	# fix other sources if eeprom is OK.

	sneeppath=$FullProg	# default path to sneep is to this script
	sneepdir=`dirname $sneeppath`  # default path to related programs


	ssu_problems=false		# no problem so far
	ssu_attempt_repair=false	# if true when finished, update serial

	# find out if backup is valid for this system:
	# validate backup file without fixing it ; get status (false==bad)
	ssu_goodbackup=false
	if backup validate false ; then
		ssu_goodbackup=true
	else
		ssu_goodbackup=false
		backup validate true	# report and fix the problem
		# at this point, there are no tags in the backup file
	fi


	# get serial from eeprom to check other sources against.
	# 	virtual eeprom via backup file is OK, because if we
	#	went straight to the real eeprom, then in an
	#	x86 non-global zone (no eeprom access) we would
	#	always fail to find it in eeprom, and then try to
	#	recover it from backup, and fail that too

	# If it is set, then proceed to loop below to check other sources.
	# If it is not set, then load from backup if backup is valid

	if ssu_eeprom=`get_value $SERIAL_TAG eeprom ` ; then
		: serial is set in eeprom - check the remaining sources
	else
		: serial is not in eeprom - may have been reset or never set

		# if backup file has the hostid from this system,
		# and has the serial in it, then reload the eeprom from backup

		if $ssu_goodbackup ; then
			: backup is from this system, check for serial
		   	if ssu_bupserial=`backup get $SERIAL_TAG`  ; then
				: serial is available from backup, load eeprom

				# we choose to load all values, not just serial
				log Restoring all sneep data using backup file

				: start by restoring the serial from backup
				# so that we do not accidentally
				# pick it up from another source
				# when we do main recovery below
				if set_value $SERIAL_TAG "$ssu_bupserial" ; then
					: serial back in eeprom
					: can continue recovery now
				else
					log -e Unable to restore $SERIAL_TAG \
						to eeprom : aborting
					# avoids more pointless error messages
					exit 1
				fi

				# now we try to recover all tags

				# Follow up with recovering all tags from
				# backup, or wherever they may be found.

				# Report commands to set values, send to shell,
				# ignore stderr in case of complaints on x86
				# about having room for only one eeprom entry
				# (but that should no longer happen)

				$sneeppath $traceflag -FTv | sh 2> $NULLFILE

				# continue with checks, just to be sure
				ssu_eeprom=`get_value $SERIAL_TAG eeprom`
				: after recovery, eeprom serial is $ssu_eeprom
			else
				: serial is not in backup, cannot load
				# missing serial will be reported in loop below
			fi
		else
			: backup may not be from this system
			log -e cannot use backup file to restore missing values \
				to eeprom
			# missing serial will be reported in loop below
		fi
	fi


	# for every other serial source, check individually
	# 	check eeprom first so that missing eeprom value will
	#	get us out of here early (see below).
	#	Also, we know that we can try to fix other sources	
	#	because eeprom was done first and must have been OK
	#	or we wouldnt still be in this loop.

	for ssu_source in eeprom `(IFS=: ; echo $SNEEPPATH)`
	do
		: ; : check source $ssu_source against $ssu_eeprom ; : ;

		if ssu_val=`get_value $SERIAL_TAG $ssu_source` ; then
			: got a value successfully from $ssu_source : $ssu_val
			# convert lower case to upper case for serial numbers
			# before checking hardware source against stored serial
			ssu_val=`echo $ssu_val | $tr '[:lower:]' '[:upper:]' `

			# see if it matches eeprom   value
			if test "$ssu_val" != "$ssu_eeprom" ; then
				log Serial in eeprom \"$ssu_eeprom\" \
					does not match serial in \
					$ssu_source \"$ssu_val\"
				ssu_problems=true
				ssu_attempt_repair=true	 # try to fix it later
			fi
		else
		 : could not get a serial value from $ssu_source
		 case "$ssu_source" in
		  eeprom )
		   log -e Chassis Serial not available from system eeprom
		   ssu_problems=true
		   # cannot repair others from eeprom
		   # if csn in backup, it was repaired above.
		   # We will keep looking for some other source
		   # with data
		   ;;
		  backup )
		   if ngzone
                   then
		    : not expected to be in zone backup
		   else
		    log Chassis Serial is not in backup file
		    ssu_problems=true
		    ssu_attempt_repair=true  # try to fix later
		   fi
		   ;;
		 explorer )
		  : no update of Explorer defaults file
		  ;;
		 cst )
		  # RFE 00056 (extension): update missing serial
		  # update missing serial if CST installed
		  $pkginfo -q SUNWcstu && ssu_attempt_repair=true
		  ;;
		 esac
		fi
	done

	# if it looked like it was fixable, give it a try
	: eeprom valid .  attempt_repair=$ssu_attempt_repair
	if $ssu_attempt_repair ; then

		if $ssu_goodbackup ; then
			: should go smoothly: eeprom and backup should align
		else
			# We used to warn of possible introduction of
			# bad tags from a foreign backup file.
			# This should never happen now that we reinitialize
			# the backup as soon as we find it is from another host
			# After that, there are no tags in the backup.

			log Warning: cannot use backup file for this recovery
		fi
			

		# Recover from some data source.
		# Set all tags found in eeprom *and backup*
		# to recover backup and other sources from eeprom.
		# (settings only in the backup will be unchanged, but
		#  will be introduced into eeprom)

		# Report commands to set values, send to shell,
		# ignore stderr in case of complaints on x86
		# about having room for only one eeprom entry

		: get all tags and values
		$sneeppath $traceflag -FTv | sh 2> $NULLFILE
	fi

	# As an additional defense against problems resulting from
	# the one-line nvramrc sometimes created on SPARC by VxVM,
	# we will set the serial number to its current value here.
	# This has the side effect of causing a one-line nvramrc on SPARC
	# to be reformatted into normal multi-line format
	# without changing anything else.
	# Fixes BUG 00039
	if ssu_eeprom=`get_value $SERIAL_TAG eeprom ` ; then
		: set serial again to current eeprom value of $ssu_eeprom
		set_eeprom_value "$ssu_eeprom" $SERIAL_TAG quiet
	fi

	return 0	# everything went as well as could be expected
}

: report_tags

report_tags()	# report tags and values; for documentation and recovery
{
	rt_tag="$1"			# optional tag passed in
	rt_specifictag=${2:-false}	# if true, just do that one tag
					# otherwise do them all

	verbose_output=$verbose		# remember whether we want verbose form

	verbose=false			# turn verbose off to prevent
					# extra messages
	if $rt_specifictag ; then

		# tag could be a comma-separated list of tags.
		# split on commas and get unique members
		rt_tags=`( IFS=, ; echo $rt_tag ) `
		rt_alltags=`
			for rt_t in $rt_tags
			do
				# if a short form of $SERIAL_TAG was used,
				# convert to the full tag name

				case "$rt_t" in
					serial | ser | csn | CSN | psn | PSN )	
						echo $SERIAL_TAG ;;
				*)
					echo $rt_t ;;
				esac
			done | sort -u
		`
	else
		# No specific tags provided:
		# Collect unique tags from all selected data sources
		# (normally this is the full set, but it could be otherwise)

		# Most sources are just for serial, so we dont even ask.
		# We add serial to eeprom/backup even if it is not there,
		# because we want to report that undesirable condition.

		rt_sources=`(IFS=: ; echo $SNEEPPATH)`
		rt_alltags=`
	 	    echo "$SERIAL_TAG"	# we assume this for all sources
		    for rt_source in $rt_sources
		    do
			: ; : collect tags from source $rt_source ; : ;
			case  "$rt_source" in
			eeprom )
				# get any number of tags from eeprom
				get_eeprom_value '.*' false false true
				;;

			backup )
				# get all tags from backup
				backup gettags '.*' ""
				;;
		
			SomethingNewAndVeryUseful )
				# insert code for any sources which can
				# store more than $SERIAL_TAG here
				: 	should never get here
				;;

			*	)
				: assume $SERIAL_TAG for $rt_source
				;;
			esac
		   done
		`  # end rt_alltags=

		# make list of unique tags
		rt_alltags=` echo "$rt_alltags" | sort -u `
	fi

	# if SERIAL_TAG is in the list, make sure that it is first in the output
	# because there is a rule that serial must be set before other tags.
	# If this is a recovery from backup, we have to put serial in eeprom
	# before we try anything else.

	case "$rt_alltags" in
	*${SERIAL_TAG}* )
		# remove serial tag
		rt_alltags=`echo "$rt_alltags" |
				grep -v "^$SERIAL_TAG\$" `
		# insert serial tag at beginning of list
		rt_alltags="$SERIAL_TAG $rt_alltags"
		;;
	esac


	# if the sneep priority path is not the default path,
	# include it in -Tv output  later, so that the sneep commands
	# obey the user's priorities and affect only desired data sources
	if test "$SNEEPPATH" != "$SNEEPPATHDEFAULT" ; then
		rt_prio="-P \"$SNEEPPATH\""
	else
		rt_prio=""
	fi

	# for each tag, print tag and value
	# for verbose printing, include command and parameters to set the value
	for rt_tag in $rt_alltags
	do
		: print value for \""$rt_tag"\"

		rt_val=`get_value "$rt_tag" `

		if $verbose_output ; then
			# output sneep command in verbose mode

			$erase && rt_val=""	# erase value if directed

			echo $FullProg $rt_prio \
				-t \""$rt_tag"\""$tab""-s \"$rt_val\""
		else
			# output tags and values
			echo \""$rt_tag"\""$tab"\""$rt_val"\"
		fi
	done
	return 0
}

: get_multi

# Print list of values with field separator
# Generally for CSV output to spreadsheet
get_multi()
{
 # Comma-separated list of tags and keywords to get
 gm_tags="$1"

 # Field separator; may be null
 gm_fieldsep="$2"
 : Get multiple fields and print with field separator
 gm_fieldvalues=""

 # Split tags on commas
 for gm_field in `(IFS="," ; echo $gm_tags )`
 do
  : ; : get val for tag \"$gm_field\" ; : ;

  # Skip bad tags
  sane_tag "$gm_field" || continue

  # Handle special pseudo-tags for commonly needed information
  # hostid and hostname we dont want to get from any other sources so we get
  # them directly
  case "$gm_field" in
   hostid)
    gm_thisval=`get_hostid`
    ;;
   hostname)
    gm_thisval=`get_hostname`
    ;;

   # Model might be found in smbios or something else so we use all of the
   # normal sources, plus the "internal" source
   model)
    gm_thisval=`get_value model "$SNEEPPATH:internal"  `
    ;;

   # Handle aliases for serial number tag
   serial | ser |  csn | CSN | psn | PSN  | $SERIAL_TAG)
    gm_thisval=`get_value "$SERIAL_TAG" `
    ;;
   *)
    # Handle all other tags
    gm_thisval=`get_value "$gm_field" `
    ;;
  esac
  if test -z "$gm_fieldvalues"
  then
   gm_fieldvalues="$gm_thisval"
  else
   gm_fieldvalues="$gm_fieldvalues$gm_fieldsep$gm_thisval"
  fi
 done
 echo "$gm_fieldvalues"
}

# Process model from something like uname -a
model_filter1()
{
 sed	-e 's/(.*)/ /g' \
  -e 's/.*SUNW,//' \
  -e 's/.*sparc//' \
  -e 's/.*SPARC-*//' \
  -e 's/.*i86pc/PC/' \
  -e 's/.*i[345]86//' \
  -e 's/Sun //' \
  -e 's/Ultra-Enterprise[- ]*/E/' \
  -e 's/Enterprise[ \-]/E/' \
  -e 's/Ultra-\([1-9][0-9]\)/E\1/' \
  -e 's/Sun.Fire-* */SF/' \
  -e 's/SF\([248]\)/V\1/' \
  -e 's/SFV/V/' \
  -e '/^E$/d' \
  -e '/^E[ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz][0-9]/s/^E//'
}

# Process model from something like prtdiag line 1
model_filter2()
{
 sed \
  -e 's/ *( *[Tt][Mm] *)/ /g' \
  -e 's/ *(.*)/ /g' \
  -e 's/System Configuration: //' \
  -e 's/SPARCstation/SS/' \
  -e 's/SPARC64 [ABCDEFGHIJKLMNOPQRSTUVWXYZ]//' \
  -e 's/.*Microsystems[ _]*//' \
  -e 's/[ _]*Inc[. _]*//' \
  -e 's/ *[Ss]erver *$//' \
  -e 's/.*SUNW,//' \
  -e 's/.*sparc[- ]*//' \
  -e 's/.*SPARC[- ]*//' \
  -e 's/.*i86pc *//' \
  -e 's/,/_/g' \
  -e 's/.*sun4[abcdefghijklmnopqrstuvwxyz]*[ _]*//' \
  -e 's/Sun.Fire[- ]*/SF/' \
  -e 's/Sun //' \
  -e 's/Fujitsu //' \
  -e 's/Ultra-Enterprise[- ]*/E/' \
  -e 's/Enterprise[ \-]\([ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz]\)/\1/' \
  -e 's/Enterprise[ \-]/E/' \
  -e 's/Ultra-\([1-9][0-9]\)/E\1/' \
  -e 's/SF\([248][89][0-9]\)\(R*\)$/V\1\2/' \
  -e 's/SFV/V/' \
  -e 's/Netra t/Netra T/' \
  -e 's/[0-9][0-9]*[- ]slot *//' \
  -e 's/ model /_/' \
  -e 's/[ _]*[1-9][0-9]*[Xx][ _]*//' \
  -e 's/[Ss].*ngine.*CP/CP/' \
  -e 's/[Ee]ngine_*//' \
  -e 's/SUN_FIRE_/SF_/' \
  -e 's/^EE/E/' \
  -e 's/^SFE/SF/' \
  -e 's/^SFX/X/' \
  -e 's/^SFT/T/' \
  -e 's/^SF_*V/V/' \
  -e 's/^SF_*X/X/' \
  -e 's/_[Ww]orkstation//' \
  -e 's/_Server_Module//' \
  -e 's/_*UPA\/PCI//' \
  -e 's/_UPA\/*//' \
  -e 's/[_ ]*[Ss][Bb][Uu][Ss][- ]*//' \
  -e '/Ultra_[0-9]/s/tra_/tra/' \
  -e 's/_T_/_T/' \
  -e '/^_*/s///' \
  -e '/^E$/d' \
  -e 's/^ *//' \
  -e 's/[_ ]*$//' \
  -e 's/  */_/g'
}

# Fabricate product "model" descriptor
get_model()
{
 $tracing && set -x

 # Pull model data from prtdiag. Take care to consume all prtdiag data with
 # "sed -n 1p" to avoid "Broken Pipe" error.
 gmod_prtd=`prtdiagf -v | sed -n 1p `
 gmod_prtd=`echo $gmod_prtd | model_filter2 `

 # Pull model data from uname -a
 gmod_uname=`get_uname -a | model_filter1`
 case "$MODEL_SELECT" in
  "")
   # Normal, default model processing
   # prtdiag generally gives good results
   gmod_model=$gmod_prtd	

   # If no model from prtdiag, use uname
   test -z "$gmod_model" && gmod_model=$gmod_uname

   # If no model at this point, fail
   test -z "$gmod_model" && return 1
   ;;

  # Remaining cases are primarily for testing and simulation
  prtdiag)
   # Return result from prtdiag
   test -z "$gmod_prtd" && return 1
   gmod_model=$gmod_prtd
   ;;
  uname)
   # Return result from uname
   test -z "$gmod_uname" && return 1
   gmod_model=$gmod_uname
   ;;
  error)
   # Return error
   return 1
   ;;
  *)
   # Return specified result
   gmod_model="$MODEL_SELECT"
   ;;
 esac

 # Fail if no data
 test -z "$gmod_model" && return 1

 # Return value, converting any whitespace to underline and multiple lines to
 # single line (by leaving $gmod_model unquoted below)
 echo $gmod_model | $tr -s '[:blank:]' '[_*]'
 return 0
}

: Begin MAIN

# Tag to use by default
SERIAL_TAG="ChassisSerialNumber"
DESIREDTAG="$SERIAL_TAG"

# If true, tag option was used
specified_tag=false

# Input serial number for setcsn
serialin=""

# If force_ucase is true then force serial numbers into upper case.
# This is the norm, but it can be overridden in the environment : $FORCE_UCASE
force_ucase=${FORCE_UCASE:-true}
test "$FORCE_UCASE" = true || FORCE_UCASE=false	# make certain of true or false

# What we get back if we cant find what we are after.
# This can be changed on the command line.
defaultdflt=unknown
defaultval=$defaultdflt	

# If true, tell more about where the data comes from
verbose=false

# Assume that we are NOT setting a value
settingvalue=false

# If true, user can override safety restrictions
override=false
			
# If true, report from all sources
allsources=false

# If true, do nothing but report tags and values
report_tags_too=false

# Output field separator
fieldsep=,

# If true and used with -T -v, erase data
erase=false

# Before we do anything else, find out if the backup file is valid and mark it
# if it is not valid and cannot be fixed. This avoids extra log messages when
# we get other backup parameters
backup validate false || backup invalidate

# The search order for retrieving the data
SNEEPPATHDEFAULT="fserial:eeprom:ipmi:smbios:prtdiag:sms:fruid:prtconf:backup:explorer:cst:prtpicl"

# Local override of default path can be stored in backup file as "sneep path"
sneeplocalpath=`backup get "sneep path" ` && SNEEPPATHDEFAULT="$sneeplocalpath"

# Use environment or default
SNEEPPATH=${SNEEPPATH:-$SNEEPPATHDEFAULT}
SNEEP_SYSLOG=${SNEEP_SYSLOG:-`backup get "sneep syslog"`}

# Define the list of possible Explorer default file locations
explorer_configs=""
if [ -n "${SIMBASE}" ]
then
 explorer_configs="${SIMBASE}/etc/explorer/default/explorer|\
${SIMBASE}/etc/opt/SUNWexplo/default/explorer"
else
 EXP_BSE=`/usr/bin/pkgparam SUNWexplu BASEDIR 2>/dev/null`
 EXP_BSE=${EXP_BSE:-/}
 if [ -n "${EXP_CONFIG}" ]
 then
  if [ "${EXP_CONFIG}" != "${EXP_BSE}etc/opt/SUNWexplo" -a \
    "${EXP_CONFIG}" != "/etc/explorer" ]
  then
   explorer_configs="${EXP_CONFIG}/default/explorer|"
  fi
 fi
 explorer_configs="${explorer_configs}\
/etc/explorer/default/explorer|\
${EXP_BSE}etc/opt/SUNWexplo/default/explorer"
fi

# If the path to an alternate explorer config file has been provided
# in the sneep backup file, then use that instead of the usual path(s)
altexplopath=`backup get "sneep explorerdefaults"`
test ! -z "$altexplopath" && explorer_configs="$altexplopath"

# Clean up on exit
trap 'RC=$?;cleanup;exit $RC' 0 1 2 15

# If we are doing a simulation AND using an explorer for the data
# then we need the defaults file from the top level of the explorer
# (and this has to override any alternate path from the sneep defaults)
# If this is a non-explorer simulation for the current domain,
# then we leave the explorer_configs relative to $SIM_BASE as it already is
if $SIMULATION && test ! -z "$EXPLO_BASE"
then
 explorer_configs="$SIMBASE/defaults"
fi
while getopts aAeFd:t:Tho:p:P:c:s:Vvx arg
do
 : arg=\""$arg"\"
 case "$arg" in
  a)
   allsources=true
   verbose=true
   ;;
  A)
   # Check data sources consistency
   sneep_serial_consistency
   exit $?
   ;;
  e)
   # Use with -T -v
   erase=true
   ;;
  h|\?)
   usage
   exit 0
   ;;
  F)
   override=true
   ;;
  t)
   # Tag to set or find in EEPROM
   specified_tag=true
   DESIREDTAG="$OPTARG"
   if test -z "$DESIREDTAG"
   then
    usage
    errmsg Cannot use null tag with -t option.
    exit 1
   fi
   ;;
  T)
   # Report tags in eeprom and backup
   report_tags_too=true
   ;;
  d)
   # Set tag to use in EEPROM. Choose what we get back if we find nothing
   defaultval="$OPTARG"	
   ;;
  o)
   # Set output field separator for multiple fields (-f)
   fieldsep="$OPTARG"
   ;;
  P)
   # Select data source(s)
   SNEEPPATH="$OPTARG" ; export SNEEPPATH
   ;;
  p)
   # Print CSN per SMS 1.4 and above. showplatform -p csn
   case "$Prog" in
    *showplatform)
     # OK - called as showplatform
     ;;
    *)
     errmsg " -p" is reserved for \"showplatform\" interface
     exit 1
     ;;
   esac
   pword="$OPTARG"
   case "$pword" in
    csn)
     # Always insist on serial with "-p csn". SMS returns "Unknown" if unknown
     defaultval=Unknown
     csn=`get_value $SERIAL_TAG `
     showplat "$csn"
     exit 0
     ;;
    *)
     usage
     exit 1
     ;;
   esac
   ;;
  c)
   # Setcsn -c <serialnumber>. We will set the serial number if "setcsn" is
   # the name but otherwise, this is a mistake
   value="$OPTARG"
   case "$Prog" in
    *setcsn)
     # Set serial if name is setcsn
     set_value "$SERIAL_TAG" "$value"
     exitval=$?
     ;;
    *)
     errmsg " -c" is reserved for \"setcsn\" interface
     exitval=1
     ;;
   esac
   exit $exitval
   ;;
  s)
   # Set an arbitrary value for an arbitrary tag <anyname> [-t anytag] -s value
   value="$OPTARG"
   settingvalue=true
   ;;
  v)
   verbose=true
   ;;
  V)
   echo "$version"
   exit 0
   ;;
  x)
   # Super-secret tracing option
   set -x
   tracing=true

   # Used when sneep is called from sneep	
   traceflag="-x"
 esac
done
shift `expr $OPTIND - 1`
: arg 1 is \"$1\"
if $report_tags_too
then

 # Report only one source per tag/value
 allsources=false

 # Report tags and values
 report_tags "$DESIREDTAG" $specified_tag
 exit $?
fi

# This script can be linked into the system startup script directories such as
# /etc/init.d and /etc/rc2.d .Perform start/stop functionality if called with
# correct keyword
case "$1" in
 stop)
  # No stop action
  exit 0
  ;;
 start | restart)
  sneep_startup $1
  exit $?
  ;;
 test | status)
  return
  ;;
esac
if $settingvalue
then
 if $override
 then
  errmsg You have chosen to override safety checks,
  errmsg and you accept full responsibility for any negative effects.
 fi

 # Where prtdiag cache is created, trap is used to capture common signals.
 # For SIGHUP, SIGINT and SIGTERM, the trap defined there will just remove
 # prtdiag cache (temporary file). So functionality remains same as here.
 # Otherwise, we may leave prtdiag cache behind in case of HUP, INT and TERM.
 # Ignore signals HUP, INT and TERM to prevent loss of data from interrupts
 # in the middle of multi-step updates
 trap "" 1 2 15
 set_value "$DESIREDTAG" "$value"
 exitval=$?

 # We might have just updated eeprom. If eeprom was successfully updated, the
 # archive must be rebuilt. As bootadm may take some time to complete, let the
 # main program exit. Archive will be updated in the background failure,if any
 # will be logged
 ( update_archive 2>&1 & ) 2>/dev/null
 exit $exitval
else

 # Get value for each tag and then output as a list
 get_multi "$DESIREDTAG" "$fieldsep"
 exit $?
fi

