#!/sbin/sh
#
# Dynamic Link Manager - Manager daemon
#
# All Rights Reserved. Copyright (C) 2001, 2007, Hitachi, Ltd.

LD_LIBRARY_PATH=/opt/DynamicLinkManager/lib:/opt/hitachi/common/lib
LANG=C
DLNKMANAGERDIR=/opt/DynamicLinkManager
DLNKMANAGERDIR_LOG=/var/opt/DynamicLinkManager
NLSPATH=${DLNKMANAGERDIR}/rc/%L/%N

export LD_LIBRARY_PATH LANG DLNKMANAGERDIR NLSPATH

mgrpid=`/usr/bin/ps -e | /usr/bin/grep dlmmgr | /usr/bin/awk '{print $1}'`

case $1 in
'start')
    if [ -f ${DLNKMANAGERDIR_LOG}/log/.disablemanager ]
    then
        exit 0
    fi

    if [ -n "${mgrpid}" ]
    then
        ISALIVE=`${DLNKMANAGERDIR}/bin/dlnkmgr view -sys -msrv -t | /usr/bin/grep Alive | /usr/bin/wc -l`
        if [ $ISALIVE -gt 0 ] ; then exit  0 ; fi
        if [ -r ${DLNKMANAGERDIR_LOG}/log/dlmmgr.pid ]
        then
            ${DLNKMANAGERDIR}/bin/dlmmgr -k
        fi
        LC=`/usr/bin/ps -e | /usr/bin/grep dlmmgr | /usr/bin/wc -l`
        CNT=6
        while [ $LC -gt 0 -a $CNT -gt 0 ]; do
            /usr/bin/sleep 1
            LC=`/usr/bin/ps -e | /usr/bin/grep dlmmgr | /usr/bin/wc -l`
            CNT=`/usr/bin/expr ${CNT} - 1`
        done
    fi

    if [ -x ${DLNKMANAGERDIR}/bin/dlmmgr ]
    then
        /usr/bin/rm ${DLNKMANAGERDIR_LOG}/log/dlmmgr.pid 2>/dev/null
        /usr/bin/rm ${DLNKMANAGERDIR_LOG}/log/DLMManagerpipe 2>/dev/null
        umask 037
        ${DLNKMANAGERDIR}/bin/dlmmgr &
        CNT=5
        while [ ! -s ${DLNKMANAGERDIR_LOG}/log/dlmmgr.pid -a $CNT -gt 0 ] ; do
            /usr/bin/sleep 1
            CNT=`/usr/bin/expr ${CNT} - 1`
        done
    fi
    ;;

'stop')
    if [ -n "${mgrpid}" ]
    then
        if [ -r ${DLNKMANAGERDIR_LOG}/log/dlmmgr.pid ]
        then
            ${DLNKMANAGERDIR}/bin/dlmmgr -k
        fi
        LC=`/usr/bin/ps -e | /usr/bin/grep dlmmgr | /usr/bin/wc -l`
        CNT=6
        while [ $LC -gt 0 -a $CNT -gt 0 ]; do
            /usr/bin/sleep 1
            LC=`/usr/bin/ps -e | /usr/bin/grep dlmmgr | /usr/bin/wc -l`
            CNT=`/usr/bin/expr ${CNT} - 1`
        done
    fi
    ;;

*)
    echo "usage: $0 {start|stop}"
    ;;
esac
exit 0
