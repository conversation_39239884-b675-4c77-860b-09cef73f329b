#
 Copyright (c) 1993, 2016, Oracle and/or its affiliates. All rights reserved.

NOTE: This directory contains legacy initialization and termination
scripts for managing services.  The preferred method of service
management is via the Service Management Facility; to read more about
SMF, consult smf(7).

For a general discussion of the mechanism used to invoke these scripts
see the file /etc/init.d/README.

After all its dependencies have been satisfied, the start method of the
SMF major milestone "svc:/milestone/multi-user:default" executes each
'S' script within /etc/rc2.d/ with the argument 'start'.  All start
scripts in the directory /etc/rcS.d/ will have been run as part of the
earlier single user milestone.  Any warnings, errors, or output from the
scripts in /etc/rc2.d/ are logged to the file:

/var/svc/log/milestone-multi-user:default.log

If the system is changing from a higher run-level (for example, through
an invocation of "init 2"), SMF executes all 'K' scripts within
/etc/rc2.d/ with the argument 'stop'.  Any warnings, errors, or output
from these scripts are logged to the file:

/var/svc/log/rc2.log

Scripts in /etc/rc2.d/ may assume the following:

	Temporary directories have been cleaned as appropriate.

	The system is a fully configured NFS client, and all NFS
	file systems are mounted.  The name service, if any, is running.
	The system logger is running.  Cron is running.

For a full list of services brought online before scripts are run, see
the output of "svcs -l svc:/milestone/multi-user:default".
