#!/bin/sh
# 
# -------------------------------------------------------------------------
# AccessControl v12.81-0
# VeRsIoN: 12.81-0 (1912) Compiled On:Script
# -------------------------------------------------------------------------

SEOSDIR="/opt/CA/AccessControl"  
##################################
##AUTO_INSERT_CODE  -- start here.
if [ $__SEOS__ ] ; then
   SEOSINIPATH=$__SEOS__
else
   if [ -d "/opt/CA/AccessControl" -a -f "/opt/CA/AccessControl/seos.ini" ] ; then
      SEOSINIPATH="/opt/CA/AccessControl"
   else
      if [ -d "/opt/CA/eTrustAccessControl" -a -f "/opt/CA/eTrustAccessControl/seos.ini" ] ; then
          SEOSINIPATH="/opt/CA/eTrustAccessControl"
      else
        if [ -d "/usr/seos/" -a -f "/usr/seos/seos.ini" ] ; then
          SEOSINIPATH="/usr/seos"
        else
          if [ -f "/etc/seos.ini" ] ; then
            if [ -h "/etc/seos.ini" ]; then
               t_file=`ls -l /etc/seos.ini | awk -F'>' '{print $2}' `
               if [ ! -f ${t_file} ]; then
                 echo "Could not find seos.ini. Set __SEOS__ and try again." #L10N
                 exit 1
               else
                 SEOSINIPATH="/etc"
               fi
            else
               SEOSINIPATH="/etc"
            fi
          else
            echo "Could not find seos.ini. Set __SEOS__ and try again." #L10N
            exit 1
          fi
        fi
      fi
   fi
fi
if [ -n "$SEOSINIPATH" ]; then
  if [ -f $SEOSINIPATH/seos.ini ] ; then
    SEOSDIR=`grep "^SEOSPATH" $SEOSINIPATH/seos.ini | grep "=" | awk -F= '{print $2}' | awk '{print $1}'`
  fi
fi

##AUTO_INSERT_CODE  -- ends here.
##################################

# Look for a vacant syscall syscall slot by searching up then down in the syscall table
# Returns:
#         syscall number if vacant slot found
#         0 if slot not found
find_syscall_slot()
{
    # build a list of free slots from /etc/name_to_sysnum
    free_slots=`awk '{print $2}' /etc/name_to_sysnum | sort -n | uniq | \
                awk 'BEGIN {x=0} {while($1>x){print x;x+=1}x+=1} END {print x}'`

    start=$1         # starting point for the search
    max_syscall=$2   # end of the table
    syscall_number=0 # initialise syscall_number as not found
    rev_slots=""     # Build a list of slots in reverse order for searching through

    # First try searching up from the starting point
    for i in $free_slots
    do
       if [ $i -ge $start -a $i -lt $max_syscall ]; then
          # found a vacant slot
          syscall_number=$i
          break
       else
          # build the reverse list of slots
          rev_slots="$i $rev_slots"
       fi
    done
    if [ $syscall_number -eq 0 ]; then
       # empty slot not found, so try searching down
       for i in $rev_slots
       do
          if [ $i -lt $start -a $i -lt $max_syscall ]; then
             # found a vacant slot
             syscall_number=$i
             break
          fi
       done
    fi
    return $syscall_number
}

# Search for available sysnum so SEOS _syscall can use it in /etc/name_to_sysnum
Check_for_sysnum()
{
  if [ -f /etc/name_to_sysnum ]
  then
    if [ -f /usr/include/sys/systm.h ] ; then
# Find maximum number allowed in systm.h
       max_syscall=`/bin/grep NSYSCALL /usr/include/sys/systm.h | awk '{print $3}'`
    fi
    max_syscall=${max_syscall:-250}
    syscall_start=101
    find_syscall_slot $syscall_start $max_syscall
    if [ $? -eq 0 ]
    then
      echo ""
      echo "S68SEOS: ERROR, AccessControl must use empty entry in /etc/name_to_sysnum."
      echo "       All the entres are occupied."
      echo "       You must free an entry in /etc/name_to_sysnum."
      echo "       Aborting S68SEOS."
      /usr/bin/logger "S68SEOS: AccessControl must use an empty entry in /etc/name_to_sysnum."
      /usr/bin/logger "S68SEOS: There are no more slots left in there so SEOS can't load."
      /usr/bin/logger "S68SEOS: Aborting S68SEOS."
      return 1
    fi
  else
      echo ""
      echo "S68SEOS: ERROR, AccessControl must use an entry in /etc/name_to_sysnum."
      echo "       /etc/name_to_sysnum does not exist on your system."
      echo "       Aborting S68SEOS."
      /usr/bin/logger "S68SEOS: ERROR: AccessControl must use an entry in /etc/name_to_sysnum."
      /usr/bin/logger "S68SEOS: /etc/name_to_sysnum does not exist on your system."
      /usr/bin/logger "S68SEOS: Aborting S68SEOS."
      return 1
  fi
  return 0
}

# insert the system call into /etc/name_to_sysnum in Solaris.
Solaris_sysnum()
{
    if [ -f /etc/name_to_sysnum ]
    then
        output=`grep SEOS_syscall /etc/name_to_sysnum`
        if [ "$?" != 0 ]
        then
            /bin/rm -f /etc/name_to_sysnum.bak
            /bin/cp /etc/name_to_sysnum /etc/name_to_sysnum.bak
            echo "SEOS_syscall $syscall_number" >> /etc/name_to_sysnum
        fi
    fi
    return 0
}

# Remove SEOS_syscall entry from /etc/name_to_sysnum
Remove_from_sysnum()
{
        if [ -f /etc/name_to_sysnum ]; then
                output=`grep SEOS_syscall /etc/name_to_sysnum`
                if [ "$?" = 0 ]; then
                        /bin/cp /etc/name_to_sysnum /etc/name_to_sysnum.bak
                        grep -v "^SEOS_syscall" /etc/name_to_sysnum.bak > /etc/name_to_sysnum
                        /bin/rm -f /etc/name_to_sysnum.bak
                        sync
                        sync
                fi
        fi
        return 0
}

#
# If Solaris 10 none global zone or ioctl usage then do nothing
#
OS_minver=`uname -r | awk -F. '{print $2}'`
my_zonename="global"

if [ $OS_minver -ge 10 ]; then
     my_zonename="`zonename`"
fi
if [ "$my_zonename" != "global" ]; then
     exit 0
fi
if [ -f $SEOSDIR/bin/seini ]; then
     use_ioctl=`$SEOSDIR/bin/seini -f SEOS_syscall.SEOS_use_ioctl` >/dev/null 2>&1
     if [ "$use_ioctl" -ne "0" ]; then
         exit 0
     fi
fi

#
# If X86 Solaris then do nothing
#
isSPARC=`uname -p`
if [ $isSPARC != "sparc" ]; then
     Remove_from_sysnum
     exit 0
fi

SHDIR=/usr/bin  # B521934 -  invoke system reboot if seos.ini says so
                #            after we modified /etc/name_to_sysnum







# Start SB2638
if grep "^SEOS_syscall" /etc/name_to_sysnum >/dev/null 2>&1; then
	true # Do nothing
else
        Check_for_sysnum
# Exit if an error occurred while processing /etc/name_to_sysnum
        if [ $? -eq 1 ]
        then
            exit 1
        fi
        Solaris_sysnum
# Exit since new entries in /etc/name_to_sysnum require a REBOOT
        if [ $? -eq 1 ]
        then
            exit 1
        fi
fi

# B810892 - From eAC5.3 it is NO longer required to run SEOS_load on system startup.
#	    Thus, we comment out this line. If needed it can be un commented once it is in
#	    /etc/rc2.d .
#$SEOSDIR/bin/SEOS_load > /dev/null
