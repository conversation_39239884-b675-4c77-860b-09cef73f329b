#!/sbin/sh
#
# @(#)hbsarc	05.00
#
# All Rights Reserved, Copyright(c) 2005, 2015, Hitachi Ltd.
#
#
# HBase Agent for Solaris (start/stop script)
#
# Check install path
#  Check Path00
 if [ -f /etc/.HBaseAgent/Path00 ] ; then
     HBSAPATH=`/usr/bin/cat /etc/.HBaseAgent/Path00 2>/dev/null | /usr/bin/grep "^HBSA_DIR=" | /usr/bin/sed 's/^HBSA_DIR=//'`
 fi
 if [ "$HBSAPATH" = "" ] ; then
    HBSAPATH="/opt/HDVM/HBaseAgent"
 fi

# Get execution user
if [ -f /etc/.HBaseAgent/admininfo ] ; then
    EXEC_USER=`/usr/bin/cat /etc/.HBaseAgent/admininfo 2>/dev/null | /usr/bin/grep "^user=" | /usr/bin/sed 's/^user=//'`
fi
if [ "${EXEC_USER}" = "" ] ; then
    EXEC_USER=root
fi

# Allowed exit values:
#	0 = success; causes "OK" to show up in checklist.
#	1 = failure; causes "FAIL" to show up in checklist.
#	2 = skip; causes "N/A" to show up in the checklist.
#	3 = reboot; causes the system to be rebooted after execution.

PATH=/usr/sbin:/usr/bin:/sbin:/usr/local/bin
export PATH

rval=0

set_return() {
	x=$?
	if [ $x -ne 0 ]; then
		echo "ERROR: starting $1, exit code: $x."
		rval=1
	fi
}

killproc() {
    $HBSAPATH/bin/hbsasrv stop -f
}

findproc() {
	pid=`ps -e | grep "$1" | grep -v "_$1" | grep -v "$1_" | sed -e 's/^  *//' -e 's/ .*//'`
}

waitproc() {
	for int in 0 1 2 3 4 5 6 7	# max about 30 sec.
	do
		sleep $int
		findproc $1
		if [ "$pid" = "" ]; then
			return
		fi
	done
	echo "ERROR: stopping, $1 failed."
	rval=1
}

case $1 in

'start_msg')
	echo "Start HBase Agent for Solaris"
	;;

'stop_msg')
	echo "Stop HBase Agent for Solaris"
	;;

'start')
	#
	# Starting agent process.
	#
	if [ -f /etc/hbsaconf ]
	then
		# read /etc/hbsaconf
		. /etc/hbsaconf
	else
		echo "ERROR: /etc/hbsaconf defaults file MISSING"
	fi

	daemon=hbsasrv
	if [ -x $HBSAPATH/bin/$daemon ]; then
		if [ "$HBSASRV" -eq 1 ]
		then
            if [ "${EXEC_USER}" = "root" ] ; then
                ${HBSAPATH}/bin/${daemon} start
            else
                sudo -u ${EXEC_USER} ${HBSAPATH}/bin/${daemon} start
            fi
            set_return ${daemon}
		else
			rval=2
		fi
	else
		echo "ERROR: cannot find the $daemon."
		rval=2
	fi
	if [ $rval -ne 0 ]; then
		exit $rval
	fi
	;;

'stop')
	#
	# Stopping agent process.
	#
	daemon=hbsa_ser
	killproc -TERM $daemon
	waitproc $daemon
	if [ $rval -ne 0 ]; then
		exit $rval
	fi
	;;

*)
	echo "ERROR: usage: $0 {start|stop|start_msg|stop_msg}"
	rval=1
	;;

esac

exit $rval
