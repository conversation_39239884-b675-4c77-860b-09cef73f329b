#!/usr/bin/ksh
# @(#)pctl       11.00.020  02/25/10             =*=
#
# NOTE:    Changes to the startup behavior of perfd daemon can be made by
#          modifying script pctl.default, which is executed by this
#          script and sets the following variable:
#
#             PCTL_START    (flag to start perfd or not)
#
# NOTE:    Do not edit this script as it is not configurable.
#
# WARNING: Changing this script in any way may lead to a system that
#          is unbootable.  Do not modify this script.
#
#------------------------------------------------------------------------
#
# This script will start up (or shut down) the perfd daemon
# for the HPPA/Glance/GlancePlus.
#
# Allowed exit values:
#       0 = success; causes "OK" to show up in checklist.
#       1 = failure; causes "FAIL" to show up in checklist.
#       2 = skip; causes "N/A" to show up in the checklist.
#           Use this value if execution of this script is overridden
#           by the use of a control variable, or if this script is not
#           appropriate to execute for some other reason.
#       3 = reboot; causes the system to be rebooted after execution.

# Input and output:
#       stdin is redirected from /dev/null
#
#       stdout and stderr are redirected to the /etc/rc.log file
#       during checklist mode, or to the console in raw mode.

################################################################################
#
# Description:  Perfd Startup/Shutdown 
#
# Copyright 1988 - 2023 Open Text 
#
################################################################################

# Check the exit value of a command run by this script.  If non-zero, the
# exit code is echoed to the log file and the return value of this script
# is set to indicate failure.

# chkconfig: 235 96 8
# description:  Start the perfd daemon


# The following commented lines are required for LINUX to update the 
# /etc/init.d/ directory. This is required to
# auto start pctl upon machine restart. 
### BEGIN INIT INFO
# Provides: perfd daemon
# Default-Start: 2 3 5
# Default-Stop: 0 6
# Description: Start the perfd daemon
### END INIT INFO

set_return() {
	x=$?
	if [ $x -ne 0 ]; then
	   echo "EXIT CODE: $x"
	   rval=1  # script FAILed
	fi
}

#########################################################################
#
#                                MAIN
#
#########################################################################


SYS="`uname -s`"

if [ "$SYS" = "AIX" -a ! -d /usr/bin ]; then	# /usr not mounted
   exit 1
fi

if [ "$SYS" = "AIX" ]; then
   INSTALL_DIR=/usr/lpp/perf/bin
else
   INSTALL_DIR=/opt/perf/bin
fi

GLANCE=$INSTALL_DIR/glance

if [ "$SYS" = "Linux" -a -f /lib/lsb/init-functions ]; then
   . /lib/lsb/init-functions
fi

# We call the LSB init functions log_failure_msg 
# and log_success_msg if available

log_failure() {
   typeset -f log_failure_msg >/dev/null 2>&1 \
    && log_failure_msg "$@" || echo "$@" " [FAILED]"
}

log_success() {
   typeset -f log_success_msg >/dev/null 2>&1 \
    && log_success_msg "$@" || echo "$@" " [OK ]"
}

# based on the first argument we call log_success or log_failure
log_msg() {
	if [ $1 -ne 0 ];  then
		shift
		log_failure $@
	else 
		shift
		log_success $@
	fi
}

PERFD=$INSTALL_DIR/perfd

# If perfd is not present in the path, then exit.
if [ ! -f $PERFD ]; then
   log_msg 1 "ERROR: perfd not found at $PERFD"
   exit 1
fi

PCTL_RUN_FILE=/var/opt/perf/datafiles/PCTL_RUN
PCTL_CTRL_CMD=$INSTALL_DIR/pctl

case "$SYS" in 
HP-UX)
	PATH=/usr/sbin:/usr/bin:/sbin
	PCTL_DEFAULT=/etc/rc.config.d/pctl.default
	;;
AIX)
	PATH=/sbin:/bin:/usr/bin:/usr/sbin:/etc:/usr/etc:/usr/lpp/perf/bin:$PATH
	PCTL_DEFAULT=/etc/default/pctl.default
	;;

Linux)
	PATH=/sbin:/bin
	if [ -d /etc/sysconfig ]; then
	   PCTL_DEFAULT=/etc/sysconfig/pctl.default
	else
	   PCTL_DEFAULT=/etc/pctl.default
	fi
	;;
SunOS)
	PATH=/usr/sbin:/usr/bin:/sbin
	PCTL_DEFAULT=/etc/default/pctl.default
	;;
*)
	echo "ERROR: Unknown platform from uname -s: $SYS"
	exit 1
	;;
esac


export PATH

rval=0

case "$1" in
'start_msg')
        echo "Starting the perfd daemon."
        ;;

'stop_msg')
        echo "Stopping the perfd daemon."
        ;;
'start')
        # source the PCTL configuration variables
        if [ -f $PCTL_DEFAULT ] ; then
           . $PCTL_DEFAULT 
        else
           log_msg 1 "ERROR: $PCTL_DEFAULT file MISSING"
        fi

        if [ -f $PCTL_RUN_FILE ]; then
           rm -f $PCTL_RUN_FILE
        fi

        # Check to see if this script is allowed to run...
        if [ "$PCTL_START" != 1 ]; then
           rval=1
        else
           if [ -x $PCTL_CTRL_CMD ]; then
              $PCTL_CTRL_CMD start
	      rval=$?
	      log_msg $rval "Starting Perfd "
           fi
        fi
        ;;

'stop')
        if [ -x $PCTL_CTRL_CMD ]; then
           $PCTL_CTRL_CMD stop 
	   rval=$?
	   log_msg $rval "Stopping Perfd "
        fi
        ;;

'restart')
        if [ -x $PCTL_CTRL_CMD ]; then
           $PCTL_CTRL_CMD restart
	   rval=$?
        fi
        ;;

'status')
        if [ -x $PCTL_CTRL_CMD ]; then
           $PCTL_CTRL_CMD status
	   rval=$?
        fi
        ;;

*)
        echo "Usage: $0 { start | stop | restart | status }"
        rval=1
        ;;

esac

exit $rval
