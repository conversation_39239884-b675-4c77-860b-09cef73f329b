#!/usr/sbin/sh
#
# Copyright (c) 1984, 1986, 1987, 1988, 1989 AT&T.
# All rights reserved.
#
#
# Copyright (c) 1989, 2011, Oracle and/or its affiliates. All rights reserved.
#

state="$1"

case "$state" in
'start')
	echo 'Starting process accounting'
	/usr/lib/acct/startup
	;;

'stop')
	echo 'Stopping process accounting'
	/usr/lib/acct/shutacct
	;;

*)
	echo "Usage: $0 { start | stop }"
	exit 1
	;;
esac
exit 0
