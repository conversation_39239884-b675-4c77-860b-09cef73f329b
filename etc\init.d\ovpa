#!/sbin/sh
# @(#)       ovpa.init       11.00.012    09FEB2010  SunOS 5.7/8/9/10   =*=
#
# NOTE:    Changes to the startup behavior of OV Performance Agent can be made by
#          modifying script /etc/default/ovpa, which is executed by this
#          script and sets the following variables:
#
#             MWA_START    (flag to start MeasureWare or not)
#             MWA_PROTOCOL   (which protocol <dce | ncs | http> to use)
#             MWA_START_COMMAND   (startup command for MeasureWare)
#
# NOTE:    This script is not configurable!  Any changes made to this
#          script will be overwritten when you upgrade to the next
#          release of HP MeasureWare Agent. Changes made to the 
#          /etc/default/ovpa script are fine and will not be
#          overwritten by the next update.
#
# WARNING: Changing this script in any way may lead to a system that
#          is unbootable.  Do not modify this script.
#
# -----------------------------------------------------------------------
#
# This script will start up (or shut down) the scopeux collection daemon
# for the HP MeasureWare Agent (MWA).
#
# Allowed exit values:
#       0 = success; causes "OK" to show up in checklist.
#       1 = failure; causes "FAIL" to show up in checklist.
#       2 = skip; causes "N/A" to show up in the checklist.
#           Use this value if execution of this script is overridden
#           by the use of a control variable, or if this script is not
#           appropriate to execute for some other reason.
#       3 = reboot; causes the system to be rebooted after execution.

# Input and output:
#       stdin is redirected from /dev/null
#
#       stdout and stderr are redirected to the /etc/rc.log file
#       during checklist mode, or to the console in raw mode.


# NOTE: If your script executes in run state 0 or state 1, then /usr might
#       not be available.  Do not attempt to access commands or files in
#       /usr unless your script executes in run state 2 or greater.  Other
#       file systems typically not mounted until run state 2 include /var
#       and /opt.
PATH=/usr/sbin:/usr/bin:/sbin
export PATH
OVPA_BOOT=1
export OVPA_BOOT
rval=0

# Check the exit value of a command run by this script.  If non-zero, the
# exit code is echoed to the log file and the return value of this script
# is set to indicate failure.

set_return() {
        x=$?
        if [ $x -ne 0 ]; then
                echo "EXIT CODE: $x"
                rval=1  # script FAILed
        fi
}

###############################################
#   main
###############################################

case $1 in
'start')
        # source the OVPA configuration variables
	if [ -f /etc/default/ovpa ] ; then
	    . /etc/default/ovpa 
	else
		echo "ERROR: /etc/default/ovpa file MISSING"
        fi

        if [ -f /var/opt/perf/datafiles/OVPA_RUN ]
        then
           rm -f /var/opt/perf/datafiles/OVPA_RUN
        fi

	# Check to see if this script is allowed to run...
        if [ "$MWA_START" != 1 ]; then
                rval=2
        else
          if [ -x /opt/perf/bin/mwa ]
          then
	    if [ "$MWA_START_COMMAND" ]
	    then
              eval $MWA_START_COMMAND
            else
	      /opt/perf/bin/mwa start
            fi
          fi
        fi
        ;;

'stop') 
       if [ -x /opt/perf/bin/mwa ]
       then
          /opt/perf/bin/mwa stop fast
       fi
       ;;

*)
        echo "usage: $0 {start|stop}"   
        rval=1
        ;;
esac

exit $rval
