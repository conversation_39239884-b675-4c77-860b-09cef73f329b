 Copyright (c) 1988, 2016, Oracle and/or its affiliates. All rights reserved.

NOTE: This directory contains legacy initialization and termination
scripts for managing services.  The preferred method of service
management is via the Service Management Facility; to read more about
SMF, consult smf(7).

File names in rc?.d directories are of the form [SK]nn<init.d filename>
where 'S' means start this job, 'K' means kill this job, and 'nn' is the
relative sequence number for killing or starting the job.  When
executing each script in one of the /etc/rc[S0-6] directories, the
/usr/sbin/rc[S0-6] script passes a single argument.  It passes the argument
'stop' for scripts prefixed with 'K' and the argument 'start' for
scripts prefixed with 'S'.  There is no harm in applying the same
sequence number to multiple scripts.  In this case the order of
execution is deterministic but unspecified.  It is recommended that
scripts be hard-linked from the same file stored in /etc/init.d/.

On earlier Solaris releases, a script named with a suffix of '.sh' would
be sourced, allowing scripts to modify the environment of other scripts
executed later.  This behavior is no longer supported; for altering the
environment in which services are run, refer to the SMF documentation.

Legacy services are reported by the default svcs(1) output, and (where
appropriate) the utility's '-p' option.  This reporting assumes a
behavior as described above, such that each 'S' script has a suitable
matching 'K' script, and they are both hard linked to a script in
/etc/init.d/.  If this is not the case, legacy service reporting may not
operate accurately; under all circumstances, the scripts are always
executed as described above.

The run-levels operate as follows:

s/S

	Read /etc/rcS.d/README for details. This run-level corresponds to
	the milestone svc:/milestone/single-user:default.

0/5/6

	All /etc/rc0.d/K* scripts are run, followed by all /etc/rc0.d/S*
	scripts. The S* scripts should only be used for cleanup during
	shutdown.

1

	If the system was in a higher run-level, all /etc/rc1.d/K* scripts
	are run. Regardless of the previous run-level, all /etc/rc1.d/S*
	scripts are run.

2

	Read /etc/rc2.d/README for details. This run-level corresponds to
	the milestone svc:/milestone/multi-user:default.

3

	Read /etc/rc3.d/README for details. This run-level corresponds to
	the milestone svc:/milestone/multi-user-server:default.

